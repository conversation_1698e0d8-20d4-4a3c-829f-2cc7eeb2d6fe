# SuperNote

A modern, AI-powered note-taking app built with React Native and Expo.

## Features

- 📝 **Smart Note Taking** - Create, edit, and organize your notes with ease
- 🤖 **AI Integration** - AI-powered summarization, voice transcription, and smart search
- 📁 **Folder Organization** - Organize notes into folders and categories
- 🎨 **Theme Support** - Light and dark theme options
- 🔐 **Secure Authentication** - User authentication with Supabase
- 📱 **Cross-Platform** - Works on iOS and Android
- ☁️ **Cloud Sync** - Automatic synchronization across devices
- 🎯 **Template Support** - Pre-built templates for different note types

## Tech Stack

- **Frontend**: React Native, Expo
- **Backend**: Supabase
- **Database**: PostgreSQL (via Supabase)
- **Authentication**: Supabase Auth
- **AI Services**: Google Gemini API
- **UI Components**: React Native Paper
- **State Management**: Zustand
- **Styling**: Custom theme system with light/dark modes

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- Expo CLI
- iOS Simulator or Android device/emulator

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd supernote
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

4. Configure your environment variables in `.env.local`:
```env
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
EXPO_PUBLIC_GEMINI_API_KEY=your_gemini_api_key
```

5. Start the development server:
```bash
npx expo start
```

6. Run on your preferred platform:
```bash
# iOS
npx expo run:ios

# Android
npx expo run:android
```

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ai/             # AI-related components
│   ├── templates/      # Note template components
│   └── ui/             # Custom UI components
├── screens/            # Screen components
│   ├── auth/           # Authentication screens
│   ├── notes/          # Note-related screens
│   ├── onboarding/     # Onboarding screens
│   └── settings/       # Settings screens
├── services/           # External service integrations
│   ├── supabase/       # Supabase client and services
│   ├── ai/             # AI service integrations
│   └── storage/        # Local storage services
├── store/              # State management stores
├── types/              # TypeScript type definitions
├── utils/              # Utility functions
└── hooks/              # Custom React hooks
```

## Key Features Implementation

### AI Integration
- Voice transcription using device microphone
- AI-powered note summarization
- Smart search with semantic understanding
- Content generation and suggestions

### Authentication
- Email/password authentication
- Social login options
- Secure session management
- User profile management

### Note Management
- Rich text editing
- Folder organization
- Tag system
- Template support
- Export functionality

## Development

### Code Style
- TypeScript for type safety
- ESLint for code linting
- Prettier for code formatting
- Follows React Native best practices

### Testing
```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage
```

### Building
```bash
# Build for production
npx expo build:production

# Build for specific platforms
npx expo build:ios
npx expo build:android
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support, email <EMAIL> or join our Discord community.

## Roadmap

- [ ] Offline mode support
- [ ] Advanced collaboration features
- [ ] Plugin system for extensions
- [ ] Advanced export formats (PDF, DOCX)
- [ ] Team workspaces
- [ ] Advanced AI features (image recognition, handwriting)

---

Built with ❤️ by the SuperNote team