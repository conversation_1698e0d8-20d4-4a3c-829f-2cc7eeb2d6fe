import { Colors } from '@/constants/theme';
import { useColorScheme } from '@/hooks/use-color-scheme';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, FlatList, StyleSheet, View } from 'react-native';
import {
  Appbar,
  Button,
  Card,
  Dialog,
  Divider,
  FAB,
  IconButton,
  Menu,
  Portal,
  Text,
  TextInput,
} from 'react-native-paper';
import { useAuthStore } from '../../src/store/authStore';
import { useFoldersStore } from '../../src/store/foldersStore';
import { useNotesStore } from '../../src/store/notesStore';
import { Folder } from '../../src/types/folder';

export default function FoldersScreen() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  const { user } = useAuthStore();
  const { folders, loadFolders, createFolder, updateFolder, deleteFolder, isLoading } = useFoldersStore();
  const { notes, loadNotes } = useNotesStore();

  const [dialogVisible, setDialogVisible] = useState(false);
  const [editingFolder, setEditingFolder] = useState<Folder | null>(null);
  const [folderName, setFolderName] = useState('');
  const [menuVisible, setMenuVisible] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      loadFolders(user.id);
      loadNotes(user.id);
    }
  }, [user, loadFolders, loadNotes]);

  const handleCreateFolder = async () => {
    if (!user || !folderName.trim()) return;

    const success = await createFolder(folderName.trim(), user.id);
    if (success) {
      setFolderName('');
      setDialogVisible(false);
    } else {
      Alert.alert('Error', 'Failed to create folder. Please try again.');
    }
  };

  const handleEditFolder = async () => {
    if (!editingFolder || !folderName.trim()) return;

    const success = await updateFolder(editingFolder.id, { name: folderName.trim() });
    if (success) {
      setEditingFolder(null);
      setFolderName('');
      setDialogVisible(false);
    } else {
      Alert.alert('Error', 'Failed to update folder. Please try again.');
    }
  };

  const handleDeleteFolder = (folder: Folder) => {
    const notesInFolder = notes.filter(note => note.folder_id === folder.id);

    Alert.alert(
      'Delete Folder',
      `Are you sure you want to delete "${folder.name}"?${
        notesInFolder.length > 0
          ? `\n\n${notesInFolder.length} note(s) will be moved to "No Folder".`
          : ''
      }`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const success = await deleteFolder(folder.id);
            if (!success) {
              Alert.alert('Error', 'Failed to delete folder. Please try again.');
            }
          },
        },
      ]
    );
  };

  const openCreateDialog = () => {
    setEditingFolder(null);
    setFolderName('');
    setDialogVisible(true);
  };

  const openEditDialog = (folder: Folder) => {
    setEditingFolder(folder);
    setFolderName(folder.name);
    setDialogVisible(true);
    setMenuVisible(null);
  };

  const getFolderNoteCount = (folderId: string) => {
    return notes.filter(note => note.folder_id === folderId).length;
  };

  const handleFolderPress = (folder: Folder) => {
    router.push({
      pathname: '/(tabs)',
      params: { folder: folder.id }
    });
  };

  const renderFolder = ({ item }: { item: Folder }) => {
    const noteCount = getFolderNoteCount(item.id);

    return (
      <Card style={styles.folderCard} onPress={() => handleFolderPress(item)}>
        <Card.Content>
          <View style={styles.folderHeader}>
            <View style={styles.folderInfo}>
              <IconButton icon="folder" size={24} style={styles.folderIcon} />
              <View style={styles.folderDetails}>
                <Text variant="titleMedium" style={styles.folderName}>
                  {item.name}
                </Text>
                <Text variant="bodySmall" style={styles.folderCount}>
                  {noteCount} note{noteCount !== 1 ? 's' : ''}
                </Text>
              </View>
            </View>

            <Menu
              visible={menuVisible === item.id}
              onDismiss={() => setMenuVisible(null)}
              anchor={
                <IconButton
                  icon="dots-vertical"
                  onPress={() => setMenuVisible(item.id)}
                />
              }
            >
              <Menu.Item
                title="Rename"
                leadingIcon="pencil"
                onPress={() => openEditDialog(item)}
              />
              <Divider />
              <Menu.Item
                title="Delete"
                leadingIcon="delete"
                onPress={() => handleDeleteFolder(item)}
              />
            </Menu>
          </View>

          <Text variant="bodySmall" style={styles.folderDate}>
            Created {new Date(item.created_at).toLocaleDateString()}
          </Text>
        </Card.Content>
      </Card>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: isDark ? Colors.dark.background : Colors.light.background }]}>
      <Appbar.Header>
        <Appbar.Content title="Folders" />
      </Appbar.Header>

      {folders.length === 0 ? (
        <View style={styles.emptyState}>
          <IconButton icon="folder-outline" size={64} style={styles.emptyIcon} />
          <Text variant="headlineSmall" style={styles.emptyTitle}>
            No Folders Yet
          </Text>
          <Text variant="bodyMedium" style={styles.emptySubtitle}>
            Create folders to organize your notes
          </Text>
          <Button
            mode="contained"
            onPress={openCreateDialog}
            style={styles.emptyButton}
            icon="folder-plus"
          >
            Create Your First Folder
          </Button>
        </View>
      ) : (
        <FlatList
          data={folders}
          keyExtractor={(item) => item.id}
          renderItem={renderFolder}
          contentContainerStyle={styles.foldersList}
          showsVerticalScrollIndicator={false}
        />
      )}

      <FAB
        icon="folder-plus"
        style={styles.fab}
        onPress={openCreateDialog}
        label="New Folder"
      />

      {/* Create/Edit Folder Dialog */}
      <Portal>
        <Dialog visible={dialogVisible} onDismiss={() => setDialogVisible(false)}>
          <Dialog.Title>
            {editingFolder ? 'Rename Folder' : 'Create New Folder'}
          </Dialog.Title>
          <Dialog.Content>
            <TextInput
              label="Folder Name"
              value={folderName}
              onChangeText={setFolderName}
              mode="outlined"
              placeholder="Enter folder name..."
              autoFocus
              onSubmitEditing={editingFolder ? handleEditFolder : handleCreateFolder}
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setDialogVisible(false)}>Cancel</Button>
            <Button
              onPress={editingFolder ? handleEditFolder : handleCreateFolder}
              disabled={!folderName.trim() || isLoading}
              loading={isLoading}
            >
              {editingFolder ? 'Rename' : 'Create'}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyIcon: {
    opacity: 0.3,
    marginBottom: 16,
  },
  emptyTitle: {
    textAlign: 'center',
    marginBottom: 8,
    fontWeight: '600',
  },
  emptySubtitle: {
    textAlign: 'center',
    opacity: 0.6,
    marginBottom: 24,
  },
  emptyButton: {
    paddingHorizontal: 16,
  },
  foldersList: {
    padding: 16,
  },
  folderCard: {
    marginBottom: 12,
    elevation: 2,
  },
  folderHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  folderInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  folderIcon: {
    margin: 0,
    marginRight: 8,
  },
  folderDetails: {
    flex: 1,
  },
  folderName: {
    fontWeight: '600',
    marginBottom: 2,
  },
  folderCount: {
    opacity: 0.7,
  },
  folderDate: {
    opacity: 0.5,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});
