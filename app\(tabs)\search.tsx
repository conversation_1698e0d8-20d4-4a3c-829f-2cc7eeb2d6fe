import { Colors } from '@/constants/theme';
import { useManualTheme } from '@/hooks/use-manual-theme';
import { useRouter } from 'expo-router';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { FlatList, ScrollView, StyleSheet, View } from 'react-native';
import {
  Appbar,
  Card,
  Chip,
  IconButton,
  Menu,
  Searchbar,
  Surface,
  Text
} from 'react-native-paper';
import { GeminiService } from '../../src/services/ai/geminiService';
import { useAuthStore } from '../../src/store/authStore';
import { useFoldersStore } from '../../src/store/foldersStore';
import { useNotesStore } from '../../src/store/notesStore';
import { Note } from '../../src/types';

type SortOption = 'updated' | 'created' | 'title';
type FilterOption = 'all' | 'pinned' | 'recent' | 'folder' | 'tag';

export default function SearchScreen() {
  const { isDark } = useManualTheme();
  const router = useRouter();
  const { user } = useAuthStore();
  const { notes, loadNotes } = useNotesStore();
  const { folders, loadFolders } = useFoldersStore();

  const [searchQuery, setSearchQuery] = useState('');
  const [activeFilter, setActiveFilter] = useState<FilterOption>('all');
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null);
  const [selectedTag, setSelectedTag] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<SortOption>('updated');
  const [sortMenuVisible, setSortMenuVisible] = useState(false);
  const [filterMenuVisible, setFilterMenuVisible] = useState(false);

  // AI Search state
  const [isAISearchEnabled, setIsAISearchEnabled] = useState(false);
  const [isAISearching, setIsAISearching] = useState(false);
  const [aiSearchResults, setAiSearchResults] = useState<Note[]>([]);

  useEffect(() => {
    if (user) {
      loadNotes(user.id);
      loadFolders(user.id);
    }
  }, [user, loadNotes, loadFolders]);

  // Get all unique tags from notes
  const allTags = useMemo(() => {
    const tagSet = new Set<string>();
    notes.forEach(note => {
      note.tags?.forEach(tag => tagSet.add(tag));
    });
    return Array.from(tagSet).sort();
  }, [notes]);

  // AI Search function
  const performAISearch = useCallback(async (query: string) => {
    if (!query.trim() || !GeminiService.isAvailable()) {
      setAiSearchResults([]);
      return;
    }

    setIsAISearching(true);
    try {
      const result = await GeminiService.smartSearch(query, notes);
      if (result.success && result.content) {
        // Parse AI response to find relevant notes
        const relevantTitles = result.content.split('\n')
          .map(line => line.trim())
          .filter(line => line.length > 0);

        // Find notes that match the AI suggestions
        const relevantNotes = notes.filter(note =>
          relevantTitles.some(title =>
            note.title.toLowerCase().includes(title.toLowerCase()) ||
            title.toLowerCase().includes(note.title.toLowerCase())
          )
        );

        setAiSearchResults(relevantNotes);
      }
    } catch (error) {
      console.error('AI Search Error:', error);
    } finally {
      setIsAISearching(false);
    }
  }, [notes]);

  // Debounced AI search
  useEffect(() => {
    if (isAISearchEnabled && searchQuery.trim()) {
      const timeoutId = setTimeout(() => {
        performAISearch(searchQuery);
      }, 1000); // 1 second delay

      return () => clearTimeout(timeoutId);
    } else {
      setAiSearchResults([]);
    }
  }, [searchQuery, isAISearchEnabled, notes, performAISearch]);

  // Filter and search notes
  const filteredNotes = useMemo(() => {
    let filtered = [...notes];

    // Apply search query
    if (searchQuery.trim()) {
      if (isAISearchEnabled && aiSearchResults.length > 0) {
        // Use AI search results
        filtered = aiSearchResults;
      } else {
        // Use traditional keyword search
        const query = searchQuery.toLowerCase();
        filtered = filtered.filter(note =>
          note.title.toLowerCase().includes(query) ||
          note.content.toLowerCase().includes(query) ||
          note.tags?.some(tag => tag.toLowerCase().includes(query))
        );
      }
    }

    // Apply filters
    switch (activeFilter) {
      case 'pinned':
        filtered = filtered.filter(note => note.is_pinned);
        break;
      case 'recent':
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        filtered = filtered.filter(note =>
          new Date(note.updated_at) > sevenDaysAgo
        );
        break;
      case 'folder':
        if (selectedFolder) {
          filtered = filtered.filter(note => note.folder_id === selectedFolder);
        }
        break;
      case 'tag':
        if (selectedTag) {
          filtered = filtered.filter(note =>
            note.tags?.includes(selectedTag)
          );
        }
        break;
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'title':
          return a.title.localeCompare(b.title);
        case 'created':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case 'updated':
        default:
          return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();
      }
    });

    return filtered;
  }, [notes, searchQuery, activeFilter, selectedFolder, selectedTag, sortBy, aiSearchResults, isAISearchEnabled]);

  const handleNotePress = (note: Note) => {
    router.push(`/note-editor?id=${note.id}`);
  };

  const handleFilterChange = (filter: FilterOption) => {
    setActiveFilter(filter);
    setSelectedFolder(null);
    setSelectedTag(null);
    setFilterMenuVisible(false);
  };

  const renderNote = ({ item }: { item: Note }) => (
    <Card style={styles.noteCard} onPress={() => handleNotePress(item)}>
      <Card.Content>
        <View style={styles.noteHeader}>
          <Text variant="titleMedium" style={styles.noteTitle} numberOfLines={1}>
            {item.title || 'Untitled'}
          </Text>
          {item.is_pinned && (
            <IconButton icon="pin" size={16} style={styles.pinIcon} />
          )}
        </View>

        <Text variant="bodyMedium" numberOfLines={3} style={styles.noteContent}>
          {item.content.replace(/<[^>]*>/g, '')} {/* Strip HTML tags for preview */}
        </Text>

        {item.tags && item.tags.length > 0 && (
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.tagsContainer}>
            {item.tags.map((tag: string, index: number) => (
              <Chip
                key={index}
                style={styles.tagChip}
                compact
                onPress={() => {
                  setSelectedTag(tag);
                  setActiveFilter('tag');
                }}
              >
                {tag}
              </Chip>
            ))}
          </ScrollView>
        )}

        <Text variant="bodySmall" style={styles.noteDate}>
          {new Date(item.updated_at).toLocaleDateString()}
        </Text>
      </Card.Content>
    </Card>
  );

  return (
    <View style={[styles.container, { backgroundColor: isDark ? Colors.dark.background : Colors.light.background }]}>
      <Appbar.Header>
        <Appbar.Content title="Search" />

        {/* AI Search Toggle */}
        {GeminiService.isAvailable() && (
          <Appbar.Action
            icon={isAISearchEnabled ? "brain" : "head-lightbulb-outline"}
            onPress={() => setIsAISearchEnabled(!isAISearchEnabled)}
            style={{
              backgroundColor: isAISearchEnabled ? '#6200EE' : 'transparent'
            }}
          />
        )}

        <Menu
          visible={sortMenuVisible}
          onDismiss={() => setSortMenuVisible(false)}
          anchor={
            <Appbar.Action
              icon="sort"
              onPress={() => setSortMenuVisible(true)}
            />
          }
        >
          <Menu.Item
            title="Recently Updated"
            onPress={() => {
              setSortBy('updated');
              setSortMenuVisible(false);
            }}
            leadingIcon={sortBy === 'updated' ? 'check' : undefined}
          />
          <Menu.Item
            title="Recently Created"
            onPress={() => {
              setSortBy('created');
              setSortMenuVisible(false);
            }}
            leadingIcon={sortBy === 'created' ? 'check' : undefined}
          />
          <Menu.Item
            title="Title A-Z"
            onPress={() => {
              setSortBy('title');
              setSortMenuVisible(false);
            }}
            leadingIcon={sortBy === 'title' ? 'check' : undefined}
          />
        </Menu>
      </Appbar.Header>

      <View style={styles.searchContainer}>
        <Searchbar
          placeholder={
            isAISearchEnabled
              ? "Ask AI to find your notes (e.g., 'notes about project planning')"
              : "Search notes, tags, content..."
          }
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={[
            styles.searchbar,
            isAISearchEnabled && [
              styles.aiSearchbar,
              {
                borderColor: isDark ? Colors.dark.primary : Colors.light.primary
              }
            ]
          ]}
          loading={isAISearching}
          icon={isAISearchEnabled ? "brain" : "magnify"}
        />
        {isAISearchEnabled && (
          <Text style={[styles.aiSearchHint, { color: isDark ? Colors.dark.primary : Colors.light.primary }]}>
            🤖 AI Search is enabled - Ask questions in natural language
          </Text>
        )}
      </View>

      {/* Filters */}
      <Surface style={styles.filtersContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <Chip
            mode={activeFilter === 'all' ? 'flat' : 'outlined'}
            style={styles.filterChip}
            onPress={() => handleFilterChange('all')}
          >
            All ({notes.length})
          </Chip>
          <Chip
            mode={activeFilter === 'pinned' ? 'flat' : 'outlined'}
            style={styles.filterChip}
            onPress={() => handleFilterChange('pinned')}
          >
            Pinned ({notes.filter(n => n.is_pinned).length})
          </Chip>
          <Chip
            mode={activeFilter === 'recent' ? 'flat' : 'outlined'}
            style={styles.filterChip}
            onPress={() => handleFilterChange('recent')}
          >
            Recent
          </Chip>

          {/* Folder Filter */}
          <Menu
            visible={filterMenuVisible}
            onDismiss={() => setFilterMenuVisible(false)}
            anchor={
              <Chip
                mode={activeFilter === 'folder' ? 'flat' : 'outlined'}
                style={styles.filterChip}
                onPress={() => setFilterMenuVisible(true)}
                icon="folder"
              >
                {selectedFolder ?
                  folders.find(f => f.id === selectedFolder)?.name || 'Folder' :
                  'Folders'
                }
              </Chip>
            }
          >
            {folders.map((folder) => (
              <Menu.Item
                key={folder.id}
                title={folder.name}
                onPress={() => {
                  setSelectedFolder(folder.id);
                  setActiveFilter('folder');
                  setFilterMenuVisible(false);
                }}
                leadingIcon="folder"
              />
            ))}
          </Menu>
        </ScrollView>
      </Surface>

      {/* Tag Chips */}
      {allTags.length > 0 && (
        <Surface style={styles.tagsSection}>
          <Text variant="labelMedium" style={styles.tagsSectionTitle}>
            Popular Tags:
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {allTags.slice(0, 10).map((tag) => (
              <Chip
                key={tag}
                mode={selectedTag === tag ? 'flat' : 'outlined'}
                style={styles.tagFilterChip}
                onPress={() => {
                  if (selectedTag === tag) {
                    setSelectedTag(null);
                    setActiveFilter('all');
                  } else {
                    setSelectedTag(tag);
                    setActiveFilter('tag');
                  }
                }}
                compact
              >
                {tag}
              </Chip>
            ))}
          </ScrollView>
        </Surface>
      )}

      {/* Results */}
      <View style={styles.resultsContainer}>
        <Text variant="bodyMedium" style={styles.resultsCount}>
          {isAISearchEnabled && searchQuery ? '🤖 ' : ''}
          {filteredNotes.length} result{filteredNotes.length !== 1 ? 's' : ''}
          {searchQuery && ` for "${searchQuery}"`}
          {isAISearchEnabled && searchQuery && ' (AI-powered)'}
        </Text>

        <FlatList
          data={filteredNotes}
          keyExtractor={(item) => item.id}
          renderItem={renderNote}
          contentContainerStyle={styles.resultsList}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            <View style={styles.emptyState}>
              <Text variant="bodyLarge" style={styles.emptyText}>
                {searchQuery ? 'No notes found' : 'Start typing to search your notes'}
              </Text>
              <Text variant="bodyMedium" style={styles.emptySubtext}>
                {searchQuery ?
                  'Try different keywords or check your filters' :
                  'Search by title, content, or tags'
                }
              </Text>
            </View>
          }
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    padding: 16,
    paddingBottom: 8,
  },
  searchbar: {
    elevation: 3,
    borderRadius: 28,
    marginHorizontal: 4,
  },
  aiSearchbar: {
    borderWidth: 2,
  },
  aiSearchHint: {
    fontSize: 12,
    marginTop: 4,
    fontStyle: 'italic',
  },
  filtersContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    elevation: 1,
  },
  filterChip: {
    marginRight: 8,
  },
  tagsSection: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    elevation: 1,
  },
  tagsSectionTitle: {
    marginBottom: 8,
    opacity: 0.7,
  },
  tagFilterChip: {
    marginRight: 6,
    marginBottom: 4,
  },
  resultsContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  resultsCount: {
    paddingVertical: 8,
    opacity: 0.7,
  },
  resultsList: {
    paddingBottom: 16,
  },
  noteCard: {
    marginBottom: 16,
    elevation: 3,
    borderRadius: 16,
    overflow: 'hidden',
  },
  noteHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  noteTitle: {
    flex: 1,
    fontWeight: '600',
  },
  pinIcon: {
    margin: 0,
  },
  noteContent: {
    marginBottom: 8,
    lineHeight: 20,
  },
  tagsContainer: {
    marginBottom: 8,
  },
  tagChip: {
    marginRight: 6,
    marginBottom: 4,
  },
  noteDate: {
    opacity: 0.6,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    textAlign: 'center',
    marginBottom: 8,
    fontWeight: '500',
  },
  emptySubtext: {
    textAlign: 'center',
    opacity: 0.6,
  },
});
