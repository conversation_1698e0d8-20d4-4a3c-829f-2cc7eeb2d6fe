import { DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useEffect, useState } from 'react';
import { Provider as PaperProvider } from 'react-native-paper';
import 'react-native-reanimated';

import { useAuthStore } from '@/src/store/authStore';

export const unstable_settings = {
  anchor: '(tabs)',
};

export default function RootLayout() {
  const {
    initializeApp,
    isLoading,
    isInitializing
  } = useAuthStore();
  const [isAppInitialized, setIsAppInitialized] = useState(false);

  useEffect(() => {
    const initializeApplication = async () => {
      await initializeApp();
      setIsAppInitialized(true);
    };

    initializeApplication();
  }, [initializeApp]);

  // Show nothing while the app is initializing
  if (!isAppInitialized || isInitializing || isLoading) {
    return null;
  }

  return (
    <PaperProvider>
      <ThemeProvider value={DefaultTheme}>
        <Stack screenOptions={{ headerShown: false }}>
          <Stack.Screen name="splash" />
          <Stack.Screen name="onboarding" />
          <Stack.Screen name="(tabs)" />
          <Stack.Screen name="note-editor" options={{ presentation: 'modal' }} />
          <Stack.Screen name="settings" options={{ presentation: 'modal' }} />
          <Stack.Screen name="auth/login" />
          <Stack.Screen name="auth/signup" />
          <Stack.Screen name="auth/forgot-password" />
        </Stack>
        <StatusBar style="auto" />
      </ThemeProvider>
    </PaperProvider>
  );
}
