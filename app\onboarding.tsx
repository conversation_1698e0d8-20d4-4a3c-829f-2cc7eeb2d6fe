import React from 'react';
import OnboardingScreen from '@/src/screens/onboarding/OnboardingScreen';
import { useAuthStore } from '@/src/store/authStore';
import { useRouter } from 'expo-router';

export default function OnboardingPage() {
  const router = useRouter();
  const { completeOnboarding, isAuthenticated } = useAuthStore();

  const handleOnboardingComplete = async () => {
    await completeOnboarding();
    
    // Navigate to appropriate screen after onboarding
    if (isAuthenticated) {
      router.replace('/(tabs)');
    } else {
      router.replace('/auth/login');
    }
  };

  return <OnboardingScreen onComplete={handleOnboardingComplete} />;
}
