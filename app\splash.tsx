import SplashScreen from '@/src/screens/SplashScreen';
import { useAuthStore } from '@/src/store/authStore';
import { useRouter } from 'expo-router';
import React from 'react';

export default function SplashScreenPage() {
  const router = useRouter();
  const { shouldShowOnboarding, isAuthenticated } = useAuthStore();

  const handleSplashFinish = () => {
    if (shouldShowOnboarding) {
      router.replace('/onboarding');
    } else if (isAuthenticated) {
      router.replace('/(tabs)');
    } else {
      router.replace('/auth/login');
    }
  };

  return <SplashScreen onFinish={handleSplashFinish} />;
}
