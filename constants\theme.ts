/**
 * SuperNote Design System
 * Comprehensive theme system with standardized spacing, typography, colors, shadows, and responsive breakpoints
 */

import { Dimensions, Platform } from 'react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Responsive Breakpoints
export const Breakpoints = {
  xs: 0,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
  xxl: 1400,
} as const;

// Spacing Scale (based on 4px grid)
export const Spacing = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  xxl: 24,
  xxxl: 32,
  xxxxl: 40,
  xxxxxl: 48,
  xxxxxxl: 64,
} as const;

// Typography Scale
export const Typography = {
  fontSize: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 28,
    xxxxl: 32,
    xxxxxl: 36,
    xxxxxxl: 48,
  },
  lineHeight: {
    xs: 16,
    sm: 20,
    md: 24,
    lg: 26,
    xl: 28,
    xxl: 32,
    xxxl: 36,
    xxxxl: 40,
    xxxxxl: 44,
    xxxxxxl: 56,
  },
  fontWeight: {
    light: '300' as const,
    normal: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
    extrabold: '800' as const,
  },
} as const;

// Border Radius Scale
export const BorderRadius = {
  none: 0,
  xs: 2,
  sm: 4,
  md: 6,
  lg: 8,
  xl: 12,
  xxl: 16,
  xxxl: 20,
  xxxxl: 24,
  round: 9999,
} as const;

// Enhanced Color Palette - Material Design 3 compliant
const primaryColor = '#6750A4';
const primaryColorDark = '#D0BCFF';

export const Colors = {
  light: {
    // Primary colors - Material Design 3
    primary: primaryColor,
    primaryVariant: '#4F378B',
    primaryContainer: '#EADDFF',
    onPrimary: '#FFFFFF',
    onPrimaryContainer: '#21005D',

    // Secondary colors
    secondary: '#625B71',
    secondaryVariant: '#7C4DFF',
    secondaryContainer: '#E8DEF8',
    onSecondary: '#FFFFFF',
    onSecondaryContainer: '#1D192B',

    // Surface colors - Enhanced for better visual hierarchy
    surface: '#FFFBFE',
    surfaceVariant: '#E7E0EC',
    surfaceContainer: '#F3EDF7',
    surfaceContainerLow: '#F7F2FA',
    surfaceContainerHigh: '#ECE6F0',
    surfaceContainerHighest: '#E6E0E9',
    onSurface: '#1D1B20',
    onSurfaceVariant: '#49454F',

    // Background colors
    background: '#FFFBFE',
    onBackground: '#1D1B20',

    // Outline colors
    outline: '#79747E',
    outlineVariant: '#CAC4D0',

    // State colors
    error: '#BA1A1A',
    errorContainer: '#FFDAD6',
    onError: '#FFFFFF',
    onErrorContainer: '#410002',

    success: '#006D3B',
    successContainer: '#7EF8B7',
    onSuccess: '#FFFFFF',
    onSuccessContainer: '#00210F',

    warning: '#7D5700',
    warningContainer: '#FFDF9C',
    onWarning: '#FFFFFF',
    onWarningContainer: '#271900',

    // Legacy support
    text: '#1C1B1F',
    tint: primaryColor,
    icon: '#49454F',
    tabIconDefault: '#79747E',
    tabIconSelected: primaryColor,
  },
  dark: {
    // Primary colors
    primary: primaryColorDark,
    primaryVariant: '#985EFF',
    primaryContainer: '#4F378B',
    onPrimary: '#371E73',
    onPrimaryContainer: '#EADDFF',

    // Secondary colors
    secondary: '#CCC2DC',
    secondaryVariant: '#7C4DFF',
    secondaryContainer: '#4A4458',
    onSecondary: '#332D41',
    onSecondaryContainer: '#E8DEF8',

    // Surface colors
    surface: '#141218',
    surfaceVariant: '#49454F',
    surfaceContainer: '#211F26',
    surfaceContainerHigh: '#2B2930',
    surfaceContainerHighest: '#36343B',
    onSurface: '#E6E0E9',
    onSurfaceVariant: '#CAC4D0',

    // Background colors
    background: '#141218',
    onBackground: '#E6E0E9',

    // Outline colors
    outline: '#938F99',
    outlineVariant: '#49454F',

    // State colors
    error: '#FFB4AB',
    errorContainer: '#93000A',
    onError: '#690005',
    onErrorContainer: '#FFDAD6',

    success: '#62DC9A',
    successContainer: '#00522C',
    onSuccess: '#003919',
    onSuccessContainer: '#7EF8B7',

    warning: '#F2B84B',
    warningContainer: '#5E4200',
    onWarning: '#3E2E00',
    onWarningContainer: '#FFDF9C',

    // Legacy support
    text: '#E6E0E9',
    tint: primaryColorDark,
    icon: '#CAC4D0',
    tabIconDefault: '#938F99',
    tabIconSelected: primaryColorDark,
  },
} as const;

// Shadow/Elevation System
export const Shadows = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  xs: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
    elevation: 1,
  },
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 3,
    elevation: 2,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.12,
    shadowRadius: 6,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.16,
    shadowRadius: 12,
    elevation: 8,
  },
  xl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 16,
  },
  xxl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 24,
  },
} as const;

// Font System
export const Fonts = Platform.select({
  ios: {
    /** iOS `UIFontDescriptorSystemDesignDefault` */
    sans: 'system-ui',
    /** iOS `UIFontDescriptorSystemDesignSerif` */
    serif: 'ui-serif',
    /** iOS `UIFontDescriptorSystemDesignRounded` */
    rounded: 'ui-rounded',
    /** iOS `UIFontDescriptorSystemDesignMonospaced` */
    mono: 'ui-monospace',
  },
  default: {
    sans: 'normal',
    serif: 'serif',
    rounded: 'normal',
    mono: 'monospace',
  },
  web: {
    sans: "system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif",
    serif: "Georgia, 'Times New Roman', serif",
    rounded: "'SF Pro Rounded', 'Hiragino Maru Gothic ProN', Meiryo, 'MS PGothic', sans-serif",
    mono: "SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace",
  },
});

// Screen Dimensions
export const Screen = {
  width: screenWidth,
  height: screenHeight,
  isSmall: screenWidth < Breakpoints.sm,
  isMedium: screenWidth >= Breakpoints.sm && screenWidth < Breakpoints.lg,
  isLarge: screenWidth >= Breakpoints.lg,
  isTablet: screenWidth >= Breakpoints.md,
  isDesktop: screenWidth >= Breakpoints.lg,
} as const;

// Component Variants
export const ComponentVariants = {
  button: {
    sizes: {
      sm: {
        paddingVertical: Spacing.sm,
        paddingHorizontal: Spacing.lg,
        fontSize: Typography.fontSize.sm,
        borderRadius: BorderRadius.md,
      },
      md: {
        paddingVertical: Spacing.md,
        paddingHorizontal: Spacing.xl,
        fontSize: Typography.fontSize.md,
        borderRadius: BorderRadius.lg,
      },
      lg: {
        paddingVertical: Spacing.lg,
        paddingHorizontal: Spacing.xxl,
        fontSize: Typography.fontSize.lg,
        borderRadius: BorderRadius.xl,
      },
    },
  },
  card: {
    variants: {
      elevated: {
        ...Shadows.md,
        borderRadius: BorderRadius.xl,
        padding: Spacing.lg,
      },
      outlined: {
        borderWidth: 1,
        borderRadius: BorderRadius.xl,
        padding: Spacing.lg,
      },
      filled: {
        borderRadius: BorderRadius.xl,
        padding: Spacing.lg,
      },
    },
  },
  input: {
    sizes: {
      sm: {
        paddingVertical: Spacing.sm,
        paddingHorizontal: Spacing.md,
        fontSize: Typography.fontSize.sm,
        borderRadius: BorderRadius.md,
      },
      md: {
        paddingVertical: Spacing.md,
        paddingHorizontal: Spacing.lg,
        fontSize: Typography.fontSize.md,
        borderRadius: BorderRadius.lg,
      },
      lg: {
        paddingVertical: Spacing.lg,
        paddingHorizontal: Spacing.xl,
        fontSize: Typography.fontSize.lg,
        borderRadius: BorderRadius.xl,
      },
    },
  },
} as const;

// Layout Constants
export const Layout = {
  headerHeight: 56,
  tabBarHeight: 60,
  fabSize: 56,
  iconSize: {
    xs: 16,
    sm: 20,
    md: 24,
    lg: 28,
    xl: 32,
    xxl: 40,
  },
  containerPadding: {
    horizontal: Spacing.lg,
    vertical: Spacing.lg,
  },
  sectionSpacing: Spacing.xxl,
  cardSpacing: Spacing.lg,
} as const;
