import { useThemeStore } from '../src/store/themeStore';

export function useManualTheme() {
  const { themeMode, setThemeMode } = useThemeStore();

  // Always return light theme for now
  const getColorScheme = (): 'light' => {
    return 'light';
  };

  return {
    colorScheme: getColorScheme(),
    themeMode,
    setThemeMode,
    isAuto: false, // No auto mode for now
    isDark: false, // Always light theme
  };
}
