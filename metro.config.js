const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Add resolver configuration for Supabase compatibility
config.resolver = {
  ...config.resolver,
  // Add alias for problematic imports
  alias: {
    ...config.resolver.alias,
    'C:\\AI\\SuperNote\\node_modules\\@expo\\metro-config\\build\\async-require.js': 'node-fetch',
  },
};

module.exports = config;