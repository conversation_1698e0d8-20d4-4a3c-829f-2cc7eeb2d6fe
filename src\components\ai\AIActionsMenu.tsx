import { Colors } from '@/constants/theme';
import { useColorScheme } from '@/hooks/use-color-scheme';
import React, { useState } from 'react';
import { Alert, StyleSheet, View } from 'react-native';
import {
  <PERSON>ton,
  Dialog,
  Divider,
  IconButton,
  List,
  Menu,
  Portal,
  Text,
  ActivityIndicator,
} from 'react-native-paper';
import { GeminiService, AIResponse, AIActionResult } from '../../services/ai/geminiService';

interface AIActionsMenuProps {
  selectedText: string;
  onTextReplace?: (newText: string) => void;
  onActionItemsFound?: (items: string[]) => void;
  onIdeasGenerated?: (ideas: string[]) => void;
}

export default function AIActionsMenu({
  selectedText,
  onTextReplace,
  onActionItemsFound,
  onIdeasGenerated,
}: AIActionsMenuProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [menuVisible, setMenuVisible] = useState(false);
  const [dialogVisible, setDialogVisible] = useState(false);
  const [dialogTitle, setDialogTitle] = useState('');
  const [dialogContent, setDialogContent] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [actionItems, setActionItems] = useState<string[]>([]);
  const [ideas, setIdeas] = useState<string[]>([]);

  const showMenu = () => setMenuVisible(true);
  const hideMenu = () => setMenuVisible(false);

  const showDialog = (title: string, content: string) => {
    setDialogTitle(title);
    setDialogContent(content);
    setDialogVisible(true);
  };

  const hideDialog = () => {
    setDialogVisible(false);
    setActionItems([]);
    setIdeas([]);
  };

  const handleAIAction = async (action: string) => {
    if (!selectedText.trim()) {
      Alert.alert('No Text Selected', 'Please select some text to use AI actions.');
      return;
    }

    if (!GeminiService.isAvailable()) {
      Alert.alert(
        'AI Service Unavailable',
        'AI features are not available. Please check your configuration.'
      );
      return;
    }

    hideMenu();
    setIsLoading(true);

    try {
      switch (action) {
        case 'improve':
          const improveResult = await GeminiService.improveWriting(selectedText);
          handleAIResponse(improveResult, 'Improved Text', true);
          break;

        case 'summarize':
          const summaryResult = await GeminiService.summarizeText(selectedText);
          handleAIResponse(summaryResult, 'Summary');
          break;

        case 'explain':
          const explainResult = await GeminiService.explainText(selectedText);
          handleAIResponse(explainResult, 'Explanation');
          break;

        case 'actionItems':
          const actionResult = await GeminiService.findActionItems(selectedText);
          handleActionItemsResponse(actionResult);
          break;

        case 'brainstorm':
          const brainstormResult = await GeminiService.brainstormIdeas(selectedText);
          handleIdeasResponse(brainstormResult);
          break;

        default:
          Alert.alert('Error', 'Unknown AI action.');
      }
    } catch (error) {
      console.error('AI Action Error:', error);
      Alert.alert('Error', 'An error occurred while processing your request.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAIResponse = (response: AIResponse, title: string, canReplace = false) => {
    if (response.success && response.content) {
      showDialog(title, response.content);
      if (canReplace && onTextReplace) {
        // Store the content for potential replacement
        setDialogContent(response.content);
      }
    } else {
      Alert.alert('Error', response.error || 'Failed to process request.');
    }
  };

  const handleActionItemsResponse = (response: AIActionResult) => {
    if (response.success && response.actionItems) {
      setActionItems(response.actionItems);
      showDialog('Action Items Found', '');
      if (onActionItemsFound) {
        onActionItemsFound(response.actionItems);
      }
    } else {
      Alert.alert('Error', response.error || 'Failed to find action items.');
    }
  };

  const handleIdeasResponse = (response: AIActionResult) => {
    if (response.success && response.ideas) {
      setIdeas(response.ideas);
      showDialog('Brainstormed Ideas', '');
      if (onIdeasGenerated) {
        onIdeasGenerated(response.ideas);
      }
    } else {
      Alert.alert('Error', response.error || 'Failed to generate ideas.');
    }
  };

  const handleReplaceText = () => {
    if (onTextReplace && dialogContent) {
      onTextReplace(dialogContent);
      hideDialog();
    }
  };

  if (!GeminiService.isAvailable()) {
    return null; // Don't render if AI is not available
  }

  return (
    <>
      <Menu
        visible={menuVisible}
        onDismiss={hideMenu}
        anchor={
          <IconButton
            icon="auto-fix"
            size={24}
            onPress={showMenu}
            style={[
              styles.aiButton,
              {
                backgroundColor: isDark ? Colors.dark.primary : Colors.light.primary
              }
            ]}
            iconColor={isDark ? Colors.dark.onPrimary : Colors.light.onPrimary}
          />
        }
      >
        <Menu.Item
          leadingIcon="text-box-edit"
          title="Improve Writing"
          onPress={() => handleAIAction('improve')}
        />
        <Menu.Item
          leadingIcon="text-box-outline"
          title="Summarize"
          onPress={() => handleAIAction('summarize')}
        />
        <Menu.Item
          leadingIcon="help-circle"
          title="Explain This"
          onPress={() => handleAIAction('explain')}
        />
        <Divider />
        <Menu.Item
          leadingIcon="checkbox-marked-circle"
          title="Find Action Items"
          onPress={() => handleAIAction('actionItems')}
        />
        <Menu.Item
          leadingIcon="lightbulb"
          title="Brainstorm Ideas"
          onPress={() => handleAIAction('brainstorm')}
        />
      </Menu>

      <Portal>
        <Dialog visible={dialogVisible} onDismiss={hideDialog}>
          <Dialog.Title>{dialogTitle}</Dialog.Title>
          <Dialog.Content>
            {isLoading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" />
                <Text style={styles.loadingText}>Processing with AI...</Text>
              </View>
            ) : (
              <>
                {dialogContent && (
                  <Text variant="bodyMedium">{dialogContent}</Text>
                )}
                
                {actionItems.length > 0 && (
                  <View>
                    {actionItems.map((item, index) => (
                      <List.Item
                        key={index}
                        title={item}
                        left={() => <List.Icon icon="checkbox-marked-circle" />}
                      />
                    ))}
                  </View>
                )}

                {ideas.length > 0 && (
                  <View>
                    {ideas.map((idea, index) => (
                      <List.Item
                        key={index}
                        title={idea}
                        left={() => <List.Icon icon="lightbulb" />}
                      />
                    ))}
                  </View>
                )}
              </>
            )}
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={hideDialog}>Close</Button>
            {dialogTitle === 'Improved Text' && onTextReplace && (
              <Button onPress={handleReplaceText}>Replace Text</Button>
            )}
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </>
  );
}

const styles = StyleSheet.create({
  aiButton: {
    margin: 4,
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    marginTop: 10,
    opacity: 0.7,
  },
});
