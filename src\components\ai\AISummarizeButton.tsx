import { Colors } from '@/constants/theme';
import { useColorScheme } from '@/hooks/use-color-scheme';
import React, { useState } from 'react';
import { Alert, StyleSheet, View } from 'react-native';
import {
  ActivityIndicator,
  Button,
  Dialog,
  IconButton,
  Portal,
  Text,
} from 'react-native-paper';
import { GeminiService } from '../../services/ai/geminiService';

interface AISummarizeButtonProps {
  noteContent: string;
  onSummaryGenerated?: (summary: string) => void;
  style?: any;
}

export default function AISummarizeButton({
  noteContent,
  onSummaryGenerated,
  style,
}: AISummarizeButtonProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [isLoading, setIsLoading] = useState(false);
  const [dialogVisible, setDialogVisible] = useState(false);
  const [summary, setSummary] = useState('');

  const handleSummarize = async () => {
    if (!noteContent.trim()) {
      Alert.alert('No Content', 'Please add some content to your note before summarizing.');
      return;
    }

    if (!GeminiService.isAvailable()) {
      Alert.alert(
        'AI Service Unavailable',
        'AI features are not available. Please check your configuration.'
      );
      return;
    }

    setIsLoading(true);

    try {
      const result = await GeminiService.summarizeText(noteContent);
      
      if (result.success && result.content) {
        setSummary(result.content);
        setDialogVisible(true);
        
        if (onSummaryGenerated) {
          onSummaryGenerated(result.content);
        }
      } else {
        Alert.alert('Error', result.error || 'Failed to generate summary.');
      }
    } catch (error) {
      console.error('Summarization Error:', error);
      Alert.alert('Error', 'An error occurred while generating the summary.');
    } finally {
      setIsLoading(false);
    }
  };

  const hideDialog = () => {
    setDialogVisible(false);
    setSummary('');
  };

  const handleCreateSummaryNote = () => {
    if (onSummaryGenerated && summary) {
      onSummaryGenerated(summary);
    }
    hideDialog();
  };

  if (!GeminiService.isAvailable()) {
    return null; // Don't render if AI is not available
  }

  return (
    <>
      <IconButton
        icon={isLoading ? 'loading' : 'text-box-outline'}
        size={24}
        onPress={handleSummarize}
        disabled={isLoading}
        style={[
          styles.summarizeButton,
          style,
          {
            backgroundColor: isDark ? Colors.dark.primary : Colors.light.primary
          }
        ]}
        iconColor={isDark ? Colors.dark.onPrimary : Colors.light.onPrimary}
      />

      <Portal>
        <Dialog visible={dialogVisible} onDismiss={hideDialog}>
          <Dialog.Title>AI Summary</Dialog.Title>
          <Dialog.Content>
            {isLoading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" />
                <Text style={styles.loadingText}>Generating summary...</Text>
              </View>
            ) : (
              <Text variant="bodyMedium">{summary}</Text>
            )}
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={hideDialog}>Close</Button>
            <Button onPress={handleCreateSummaryNote}>Create Summary Note</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </>
  );
}

const styles = StyleSheet.create({
  summarizeButton: {
    margin: 4,
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    marginTop: 10,
    opacity: 0.7,
  },
});
