import { Colors } from '@/constants/theme';
import { useColorScheme } from '@/hooks/use-color-scheme';
import {
    RecordingPresets,
    requestRecordingPermissionsAsync,
    setAudioModeAsync,
    useAudioRecorder,
    useAudioRecorderState,
} from 'expo-audio';
import React, { useEffect, useState } from 'react';
import { Alert, StyleSheet, View } from 'react-native';
import {
    ActivityIndicator,
    IconButton,
    Text,
} from 'react-native-paper';

interface VoiceRecorderProps {
  onTranscriptionComplete?: (transcription: string) => void;
  style?: any;
}

export default function VoiceRecorder({
  onTranscriptionComplete,
  style,
}: VoiceRecorderProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const audioRecorder = useAudioRecorder(RecordingPresets.HIGH_QUALITY);
  const recorderState = useAudioRecorderState(audioRecorder);
  const [isProcessing, setIsProcessing] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);

  useEffect(() => {
    let interval: ReturnType<typeof setInterval>;

    if (recorderState.isRecording) {
      interval = setInterval(() => {
        setRecordingDuration(prev => prev + 1);
      }, 1000);
    } else {
      setRecordingDuration(0);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [recorderState.isRecording]);

  useEffect(() => {
    const setupAudio = async () => {
      try {
        const { status } = await requestRecordingPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert(
            'Permission Required',
            'Please grant microphone permission to use voice recording.'
          );
          return;
        }

        await setAudioModeAsync({
          allowsRecording: true,
          playsInSilentMode: true,
        });
      } catch (error) {
        console.error('Error setting up audio:', error);
      }
    };

    setupAudio();
  }, []);

  const startRecording = async () => {
    try {
      await audioRecorder.prepareToRecordAsync();
      audioRecorder.record();
    } catch (error) {
      console.error('Failed to start recording:', error);
      Alert.alert('Error', 'Failed to start recording. Please try again.');
    }
  };

  const stopRecording = async () => {
    try {
      await audioRecorder.stop();

      const uri = audioRecorder.uri;
      if (uri) {
        await processAudioFile(uri);
      }
    } catch (error) {
      console.error('Failed to stop recording:', error);
      Alert.alert('Error', 'Failed to stop recording. Please try again.');
    }
  };

  const processAudioFile = async (uri: string) => {
    setIsProcessing(true);

    try {
      // TODO: Implement real speech-to-text transcription
      // For now, show a message that transcription is not available
      Alert.alert(
        'Transcription Not Available',
        'Speech-to-text transcription is not yet implemented. The audio file has been recorded but transcription requires integration with a speech recognition service like Google Speech-to-Text, Azure Speech Services, or OpenAI Whisper.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Failed to process audio:', error);
      Alert.alert('Error', 'Failed to process audio file. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleToggleRecording = () => {
    if (recorderState.isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  };


  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <>
      <View style={[styles.container, style]}>
        <IconButton
          icon={recorderState.isRecording ? 'stop' : 'microphone'}
          size={24}
          onPress={handleToggleRecording}
          disabled={isProcessing}
          style={[
            styles.recordButton,
            recorderState.isRecording && styles.recordingButton,
            {
              backgroundColor: recorderState.isRecording
                ? (isDark ? Colors.dark.error : Colors.light.error)
                : (isDark ? Colors.dark.primary : Colors.light.primary)
            }
          ]}
          iconColor={isDark ? Colors.dark.onPrimary : Colors.light.onPrimary}
        />
        {recorderState.isRecording && (
          <Text style={[styles.durationText, { color: isDark ? Colors.dark.error : Colors.light.error }]}>
            {formatDuration(recordingDuration)}
          </Text>
        )}
        {isProcessing && (
          <ActivityIndicator size="small" style={styles.processingIndicator} />
        )}
      </View>

    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  recordButton: {
    margin: 4,
  },
  recordingButton: {
  },
  durationText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '600',
  },
  processingIndicator: {
    marginLeft: 8,
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    marginTop: 10,
    opacity: 0.7,
  },
});
