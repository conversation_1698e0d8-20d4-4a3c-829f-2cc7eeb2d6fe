import React, { useEffect } from 'react';
import { StyleSheet, View } from 'react-native';
import { Text } from 'react-native-paper';
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming
} from 'react-native-reanimated';
import { Colors, Shadows } from '../../constants/theme';
import { useColorScheme } from '../../hooks/use-color-scheme';



interface SplashScreenProps {
  onFinish?: () => void;
}

export default function SplashScreen({ onFinish }: SplashScreenProps) {
  const colorScheme = useColorScheme();
  const opacity = useSharedValue(0);
  const scale = useSharedValue(0.8);
  const logoRotation = useSharedValue(0);

  useEffect(() => {
    // Start animations
    opacity.value = withTiming(1, { duration: 800 });
    scale.value = withTiming(1, { duration: 800, easing: Easing.out(Easing.cubic) });

    // Logo rotation animation
    logoRotation.value = withRepeat(
      withTiming(360, { duration: 2000, easing: Easing.linear }),
      -1,
      false
    );

    // Auto finish after 3 seconds
    const timer = setTimeout(() => {
      onFinish?.();
    }, 3000);

    return () => clearTimeout(timer);
  }, [logoRotation, onFinish, opacity, scale]);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [{ scale: scale.value }],
  }));

  const logoAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${logoRotation.value}deg` }],
  }));

  const isDark = colorScheme === 'dark';

  return (
    <View style={[styles.container, { backgroundColor: isDark ? Colors.dark.background : Colors.light.background }]}>
      <Animated.View style={[styles.content, animatedStyle]}>
        {/* Logo */}
        <Animated.View style={[styles.logoContainer, logoAnimatedStyle]}>
          <View style={[styles.logo, { backgroundColor: isDark ? Colors.dark.primary : Colors.light.primary }]}>
            <Text style={[styles.logoText, { color: isDark ? Colors.dark.onPrimary : Colors.light.onPrimary }]}>SN</Text>
          </View>
        </Animated.View>

        {/* App Name */}
        <Text style={[styles.appName, { color: isDark ? Colors.dark.onBackground : Colors.light.onBackground }]}>
          SuperNote
        </Text>

        {/* Tagline */}
        <Text style={[styles.tagline, { color: isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant }]}>
          Your AI-Powered Note Taking Companion
        </Text>

        {/* Loading indicator */}
        <View style={styles.loadingContainer}>
          <View style={[styles.loadingDot, { backgroundColor: isDark ? Colors.dark.primary : Colors.light.primary }]} />
          <View style={[styles.loadingDot, { backgroundColor: isDark ? Colors.dark.primary : Colors.light.primary }]} />
          <View style={[styles.loadingDot, { backgroundColor: isDark ? Colors.dark.primary : Colors.light.primary }]} />
        </View>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoContainer: {
    marginBottom: 30,
  },
  logo: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    ...Shadows.lg,
  },
  logoText: {
    fontSize: 36,
    fontWeight: 'bold',
  },
  appName: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  tagline: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 50,
    paddingHorizontal: 40,
  },
  loadingContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
});
