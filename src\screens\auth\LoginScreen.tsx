import { BorderRadius, Colors, Shadows, Spacing, Typography } from '@/constants/theme';
import { useColorScheme } from '@/hooks/use-color-scheme';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  View
} from 'react-native';
import {
  Button,
  Paragraph,
  Text,
  TextInput,
  Title
} from 'react-native-paper';
import { useAuthStore } from '../../store/authStore';
import { LoginForm } from '../../types';

export default function LoginScreen() {
  const router = useRouter();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { signIn, isLoading } = useAuthStore();

  const [form, setForm] = useState<LoginForm>({
    email: '',
    password: '',
  });
  
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState<Partial<LoginForm>>({});

  const validateForm = (): boolean => {
    const newErrors: Partial<LoginForm> = {};
    
    if (!form.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(form.email)) {
      newErrors.email = 'Please enter a valid email';
    }
    
    if (!form.password) {
      newErrors.password = 'Password is required';
    } else if (form.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSignIn = async () => {
    if (!validateForm()) return;
    
    try {
      const success = await signIn(form);
      
      if (success) {
        router.replace('/(tabs)');
      } else {
        Alert.alert('Error', 'Invalid email or password. Please try again.');
      }
    } catch (error) {
      console.error('Login error:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    }
  };

  const handleForgotPassword = () => {
    router.push('/auth/forgot-password');
  };

  const handleSignUp = () => {
    router.push('/auth/signup');
  };

  return (
    <View style={[styles.container, { backgroundColor: isDark ? Colors.dark.background : Colors.light.background }]}>
      <KeyboardAvoidingView
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <View style={[styles.content, { maxWidth: 400 }]}>
            <View style={styles.header}>
                <Title style={[styles.title, { color: isDark ? Colors.dark.onBackground : Colors.light.onBackground }]}>
                  Welcome to SuperNote
                </Title>
                <Paragraph style={[styles.subtitle, { color: isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant }]}>
                  Sign in to access your AI-powered notes
                </Paragraph>
            </View>

              <View style={styles.card}>
                <View>
                  <View>
                    <TextInput
                      label="Email"
                      value={form.email}
                      onChangeText={(text) => setForm({ ...form, email: text })}
                      mode="outlined"
                      keyboardType="email-address"
                      autoCapitalize="none"
                      autoComplete="email"
                      error={!!errors.email}
                      style={styles.input}
                      outlineStyle={styles.inputOutline}
                    />
                    {errors.email && (
                      <Text style={[styles.errorText, { color: isDark ? Colors.dark.error : Colors.light.error }]}>
                        {errors.email}
                      </Text>
                    )}

                    <TextInput
                      label="Password"
                      value={form.password}
                      onChangeText={(text) => setForm({ ...form, password: text })}
                      mode="outlined"
                      secureTextEntry={!showPassword}
                      autoComplete="password"
                      error={!!errors.password}
                      style={styles.input}
                      outlineStyle={styles.inputOutline}
                      right={
                        <TextInput.Icon
                          icon={showPassword ? 'eye-off' : 'eye'}
                          onPress={() => setShowPassword(!showPassword)}
                        />
                      }
                    />
                    {errors.password && (
                      <Text style={[styles.errorText, { color: isDark ? Colors.dark.error : Colors.light.error }]}>
                        {errors.password}
                      </Text>
                    )}
                  </View>

                  <View>
                    <Button
                      mode="contained"
                      onPress={handleSignIn}
                      loading={isLoading}
                      disabled={isLoading}
                      style={[styles.signInButton, { backgroundColor: isDark ? Colors.dark.primary : Colors.light.primary }]}
                      contentStyle={styles.buttonContent}
                      labelStyle={[styles.buttonLabel, { color: isDark ? Colors.dark.onPrimary : Colors.light.onPrimary }]}
                    >
                      {isLoading ? 'Signing In...' : 'Sign In'}
                    </Button>

                    <Button
                      mode="text"
                      onPress={handleForgotPassword}
                      style={styles.forgotButton}
                      labelStyle={[styles.forgotButtonLabel, { color: isDark ? Colors.dark.primary : Colors.light.primary }]}
                    >
                      Forgot Password?
                    </Button>
                  </View>
                </View>
              </View>

              <View style={styles.spacer} />

              <View style={styles.signUpSection}>
                <Paragraph style={[styles.signUpText, { color: isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant }]}>
                  Don&apos;t have an account?
                </Paragraph>
                <Button
                  mode="outlined"
                  onPress={handleSignUp}
                  style={[styles.signUpButton, { borderColor: isDark ? Colors.dark.outline : Colors.light.outline }]}
                  contentStyle={styles.buttonContent}
                  labelStyle={[styles.signUpButtonLabel, { color: isDark ? Colors.dark.primary : Colors.light.primary }]}
                >
                  Create Account
                </Button>
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingVertical: Spacing.xxl,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    minHeight: '100%',
  },
  header: {
    alignItems: 'center',
  },
  title: {
    fontSize: Typography.fontSize.xxxxl,
    fontWeight: Typography.fontWeight.bold,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: Typography.fontSize.lg,
    textAlign: 'center',
    lineHeight: Typography.lineHeight.lg,
  },
  card: {
    width: '100%',
    ...Shadows.lg,
  },
  input: {
    fontSize: Typography.fontSize.md,
  },
  inputOutline: {
    borderRadius: BorderRadius.lg,
  },
  errorText: {
    fontSize: Typography.fontSize.sm,
    marginLeft: Spacing.md,
  },
  signInButton: {
    borderRadius: BorderRadius.lg,
    ...Shadows.sm,
  },
  buttonContent: {
    paddingVertical: Spacing.md,
  },
  buttonLabel: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.semibold,
  },
  forgotButton: {
    alignSelf: 'center',
  },
  forgotButtonLabel: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.medium,
  },
  spacer: {
    height: Spacing.xl,
  },
  signUpSection: {
    alignItems: 'center',
    width: '100%',
  },
  signUpText: {
    textAlign: 'center',
    fontSize: Typography.fontSize.md,
  },
  signUpButton: {
    width: '100%',
    borderRadius: BorderRadius.lg,
  },
  signUpButtonLabel: {
    fontSize: Typography.fontSize.md,
    fontWeight: Typography.fontWeight.semibold,
  },
});
