import { Colors } from '@/constants/theme';
import { useColorScheme } from '@/hooks/use-color-scheme';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  View,
} from 'react-native';
import {
  Button,
  Card,
  Checkbox,
  Divider,
  Paragraph,
  Text,
  TextInput,
  Title,
} from 'react-native-paper';
import { useAuthStore } from '../../store/authStore';
import { SignupForm } from '../../types';

export default function SignupScreen() {
  const router = useRouter();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { signUp, isLoading } = useAuthStore();

  const [form, setForm] = useState<SignupForm>({
    email: '',
    password: '',
    confirmPassword: '',
    fullName: '',
  });
  
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [errors, setErrors] = useState<Partial<SignupForm & { terms: string }>>({});

  const validateForm = (): boolean => {
    const newErrors: Partial<SignupForm & { terms: string }> = {};
    
    if (!form.fullName.trim()) {
      newErrors.fullName = 'Full name is required';
    } else if (form.fullName.trim().length < 2) {
      newErrors.fullName = 'Full name must be at least 2 characters';
    }
    
    if (!form.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(form.email)) {
      newErrors.email = 'Please enter a valid email';
    }
    
    if (!form.password) {
      newErrors.password = 'Password is required';
    } else if (form.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }
    
    if (!form.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (form.password !== form.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }
    
    if (!acceptTerms) {
      newErrors.terms = 'Please accept the terms and conditions';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSignUp = async () => {
    if (!validateForm()) return;
    
    try {
      const success = await signUp(form);
      
      if (success) {
        Alert.alert(
          'Account Created',
          'Your account has been created successfully! Please check your email to verify your account.',
          [
            {
              text: 'OK',
              onPress: () => router.replace('/(tabs)'),
            },
          ]
        );
      } else {
        Alert.alert('Error', 'Failed to create account. Please try again.');
      }
    } catch (error) {
      console.error('Signup error:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    }
  };

  const handleSignIn = () => {
    router.push('/auth/login');
  };

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: isDark ? Colors.dark.background : Colors.light.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Title style={styles.title}>Create Account</Title>
            <Paragraph style={styles.subtitle}>
              Join SuperNote and start taking smarter notes
            </Paragraph>
          </View>

          <Card style={styles.card}>
            <Card.Content>
              <TextInput
                label="Full Name"
                value={form.fullName}
                onChangeText={(text) => setForm({ ...form, fullName: text })}
                mode="outlined"
                autoCapitalize="words"
                autoComplete="name"
                error={!!errors.fullName}
                style={styles.input}
              />
              {errors.fullName && (
                <Text style={[styles.errorText, { color: isDark ? Colors.dark.error : Colors.light.error }]}>{errors.fullName}</Text>
              )}

              <TextInput
                label="Email"
                value={form.email}
                onChangeText={(text) => setForm({ ...form, email: text })}
                mode="outlined"
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete="email"
                error={!!errors.email}
                style={styles.input}
              />
              {errors.email && (
                <Text style={[styles.errorText, { color: isDark ? Colors.dark.error : Colors.light.error }]}>{errors.email}</Text>
              )}

              <TextInput
                label="Password"
                value={form.password}
                onChangeText={(text) => setForm({ ...form, password: text })}
                mode="outlined"
                secureTextEntry={!showPassword}
                autoComplete="password-new"
                error={!!errors.password}
                style={styles.input}
                right={
                  <TextInput.Icon
                    icon={showPassword ? 'eye-off' : 'eye'}
                    onPress={() => setShowPassword(!showPassword)}
                  />
                }
              />
              {errors.password && (
                <Text style={[styles.errorText, { color: isDark ? Colors.dark.error : Colors.light.error }]}>{errors.password}</Text>
              )}

              <TextInput
                label="Confirm Password"
                value={form.confirmPassword}
                onChangeText={(text) => setForm({ ...form, confirmPassword: text })}
                mode="outlined"
                secureTextEntry={!showConfirmPassword}
                autoComplete="password-new"
                error={!!errors.confirmPassword}
                style={styles.input}
                right={
                  <TextInput.Icon
                    icon={showConfirmPassword ? 'eye-off' : 'eye'}
                    onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                  />
                }
              />
              {errors.confirmPassword && (
                <Text style={[styles.errorText, { color: isDark ? Colors.dark.error : Colors.light.error }]}>{errors.confirmPassword}</Text>
              )}

              <View style={styles.termsContainer}>
                <Checkbox
                  status={acceptTerms ? 'checked' : 'unchecked'}
                  onPress={() => setAcceptTerms(!acceptTerms)}
                />
                <Text style={styles.termsText}>
                  I agree to the{' '}
                  <Text style={[styles.linkText, { color: isDark ? Colors.dark.primary : Colors.light.primary }]}>Terms of Service</Text>
                  {' '}and{' '}
                  <Text style={[styles.linkText, { color: isDark ? Colors.dark.primary : Colors.light.primary }]}>Privacy Policy</Text>
                </Text>
              </View>
              {errors.terms && (
                <Text style={[styles.errorText, { color: isDark ? Colors.dark.error : Colors.light.error }]}>{errors.terms}</Text>
              )}

              <Button
                mode="contained"
                onPress={handleSignUp}
                loading={isLoading}
                disabled={isLoading}
                style={styles.signUpButton}
                contentStyle={styles.buttonContent}
              >
                {isLoading ? 'Creating Account...' : 'Create Account'}
              </Button>
            </Card.Content>
          </Card>

          <Divider style={styles.divider} />

          <View style={styles.signInSection}>
            <Paragraph style={styles.signInText}>
              Already have an account?
            </Paragraph>
            <Button
              mode="outlined"
              onPress={handleSignIn}
              style={styles.signInButton}
              contentStyle={styles.buttonContent}
            >
              Sign In
            </Button>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.7,
  },
  card: {
    marginBottom: 20,
  },
  input: {
    marginBottom: 8,
  },
  errorText: {
    fontSize: 12,
    marginBottom: 16,
    marginLeft: 12,
  },
  termsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 16,
  },
  termsText: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
  },
  linkText: {
    textDecorationLine: 'underline',
  },
  signUpButton: {
    marginTop: 8,
  },
  buttonContent: {
    paddingVertical: 8,
  },
  divider: {
    marginVertical: 20,
  },
  signInSection: {
    alignItems: 'center',
  },
  signInText: {
    marginBottom: 12,
    opacity: 0.7,
  },
  signInButton: {
    width: '100%',
  },
});
