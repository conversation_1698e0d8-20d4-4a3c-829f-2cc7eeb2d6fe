import { BorderRadius, Colors, Shadows, Spacing, Typography } from '@/constants/theme';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  Alert,
  FlatList,
  RefreshControl,
  StyleSheet,
  TouchableOpacity,
  View
} from 'react-native';
import {
  ActivityIndicator,
  Appbar,
  Banner,
  Button,
  Card,
  Chip,
  Dialog,
  Divider,
  IconButton,
  Menu,
  Portal,
  Text,
  TextInput
} from 'react-native-paper';
import { useManualTheme } from '../../../hooks/use-manual-theme';

import { useAuthStore } from '../../store/authStore';
import { useFoldersStore } from '../../store/foldersStore';
import { useNotesStore } from '../../store/notesStore';
import { Note } from '../../types';
import { Folder } from '../../types/folder';

export default function HomeScreen() {
  const { colorScheme } = useManualTheme();
  const router = useRouter();
  const params = useLocalSearchParams();
  const isDark = (colorScheme as 'light' | 'dark') === 'dark';

  const { user, databaseReady, databaseError, testDatabaseConnection } = useAuthStore();
  const {
    notes,
    isLoading,
    loadNotes,
    setCurrentNote,
    deleteNote
  } = useNotesStore();
  const { folders, loadFolders, createFolder, updateFolder, deleteFolder, isLoading: foldersLoading } = useFoldersStore();

  // View state
  const [currentView, setCurrentView] = useState<'notes' | 'folders'>('notes');
  const [refreshing, setRefreshing] = useState(false);

  // Folder management state
  const [dialogVisible, setDialogVisible] = useState(false);
  const [editingFolder, setEditingFolder] = useState<Folder | null>(null);
  const [folderName, setFolderName] = useState('');
  const [folderMenuVisible, setFolderMenuVisible] = useState<string | null>(null);
  const [noteMenuVisible, setNoteMenuVisible] = useState<string | null>(null);

  // Get folder filter from params
  const selectedFolderId = params.folder as string | undefined;

  useEffect(() => {
    if (user) {
      loadNotes(user.id);
      loadFolders(user.id);
    }
  }, [user, loadNotes, loadFolders]);

  const handleRefresh = async () => {
    if (!user) return;
    
    setRefreshing(true);
    await loadNotes(user.id);
    setRefreshing(false);
  };



  const handleNotePress = (note: Note) => {
    setCurrentNote(note);
    router.push(`/note-editor?noteId=${note.id}`);
  };

  const handleCreateNote = () => {
    setCurrentNote(null);
    router.push('/note-editor');
  };



  // Folder management functions
  const handleCreateFolder = async () => {
    if (!user || !folderName.trim()) return;

    const success = await createFolder(folderName.trim(), user.id);
    if (success) {
      setFolderName('');
      setDialogVisible(false);
    } else {
      Alert.alert('Error', 'Failed to create folder. Please try again.');
    }
  };

  const handleEditFolder = async () => {
    if (!editingFolder || !folderName.trim()) return;

    const success = await updateFolder(editingFolder.id, { name: folderName.trim() });
    if (success) {
      setEditingFolder(null);
      setFolderName('');
      setDialogVisible(false);
    } else {
      Alert.alert('Error', 'Failed to update folder. Please try again.');
    }
  };

  const handleDeleteNote = (note: Note) => {
    Alert.alert(
      'Delete Note',
      `Are you sure you want to delete "${note.title || 'Untitled Note'}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const success = await deleteNote(note.id);
            if (!success) {
              Alert.alert('Error', 'Failed to delete note. Please try again.');
            }
          },
        },
      ]
    );
  };

  const handleDeleteFolder = (folder: Folder) => {
    const notesInFolder = notes.filter(note => note.folder_id === folder.id);

    Alert.alert(
      'Delete Folder',
      `Are you sure you want to delete "${folder.name}"?${
        notesInFolder.length > 0
          ? `\n\n${notesInFolder.length} note(s) will be moved to "No Folder".`
          : ''
      }`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const success = await deleteFolder(folder.id);
            if (!success) {
              Alert.alert('Error', 'Failed to delete folder. Please try again.');
            }
          },
        },
      ]
    );
  };

  const openCreateDialog = () => {
    setEditingFolder(null);
    setFolderName('');
    setDialogVisible(true);
  };

  const openEditDialog = (folder: Folder) => {
    setEditingFolder(folder);
    setFolderName(folder.name);
    setDialogVisible(true);
    setFolderMenuVisible(null);
  };

  const getFolderNoteCount = (folderId: string) => {
    return notes.filter(note => note.folder_id === folderId).length;
  };

  const handleFolderPress = (folder: Folder) => {
    setCurrentView('notes');
    // You can add folder filtering logic here if needed
  };



  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return 'Today';
    } else if (diffDays === 2) {
      return 'Yesterday';
    } else if (diffDays <= 7) {
      return `${diffDays - 1} days ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const renderNoteItem = ({ item }: { item: Note }) => (
    <Card
      style={[
        styles.noteCard,
        {
          backgroundColor: isDark ? Colors.dark.surface : Colors.light.surface,
          ...Shadows.sm,
        }
      ]}
      onPress={() => handleNotePress(item)}
    >
      <Card.Content style={styles.noteCardContent}>
        <View style={styles.noteHeader}>
          <Text
            variant="titleMedium"
            style={[
              styles.noteTitle,
              { color: isDark ? Colors.dark.onSurface : Colors.light.onSurface }
            ]}
            numberOfLines={1}
          >
            {item.title || 'Untitled'}
          </Text>
          <View style={styles.noteActions}>
            {item.is_pinned && (
              <IconButton
                icon="pin"
                size={18}
                iconColor={isDark ? Colors.dark.primary : Colors.light.primary}
                style={styles.pinIcon}
              />
            )}
            <Menu
              visible={noteMenuVisible === item.id}
              onDismiss={() => setNoteMenuVisible(null)}
              anchor={
                <IconButton
                  icon="dots-vertical"
                  size={18}
                  iconColor={isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant}
                  onPress={() => setNoteMenuVisible(item.id)}
                  style={styles.noteMenuButton}
                />
              }
              contentStyle={[
                styles.menuContent,
                { backgroundColor: isDark ? Colors.dark.surface : Colors.light.surface }
              ]}
              anchorPosition="bottom"
            >
              <Menu.Item
                title="Edit"
                leadingIcon="pencil"
                onPress={() => {
                  setNoteMenuVisible(null);
                  handleNotePress(item);
                }}
                titleStyle={{ color: isDark ? Colors.dark.onSurface : Colors.light.onSurface }}
              />
              <Divider style={{ backgroundColor: isDark ? Colors.dark.outline : Colors.light.outline }} />
              <Menu.Item
                title="Delete"
                leadingIcon="delete"
                onPress={() => {
                  setNoteMenuVisible(null);
                  handleDeleteNote(item);
                }}
                titleStyle={{ color: isDark ? Colors.dark.error : Colors.light.error }}
              />
            </Menu>
          </View>
        </View>

        <View style={styles.spacer} />

        <Text
          variant="bodyMedium"
          style={[
            styles.noteContent,
            { color: isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant }
          ]}
          numberOfLines={3}
        >
          {item.content.replace(/<[^>]*>/g, '').trim() || 'No content'}
        </Text>

        <View style={styles.noteFooter}>
          <View style={styles.noteTags}>
            {item.tags.slice(0, 2).map((tag, index) => (
              <Chip key={index} style={styles.tag} compact>
                {tag}
              </Chip>
            ))}
            {item.tags.length > 2 && (
              <Text style={styles.moreTagsText}>+{item.tags.length - 2}</Text>
            )}
          </View>

          <Text variant="bodySmall" style={styles.noteDate}>
            {formatDate(item.updated_at)}
          </Text>
        </View>
      </Card.Content>
    </Card>
  );

  const renderFolder = ({ item }: { item: Folder }) => {
    const noteCount = getFolderNoteCount(item.id);

    return (
      <Card style={styles.folderCard} onPress={() => handleFolderPress(item)}>
        <Card.Content>
          <View style={styles.folderHeader}>
            <View style={styles.folderInfo}>
              <IconButton icon="folder" size={24} style={styles.folderIcon} />
              <View style={styles.folderDetails}>
                <Text variant="titleMedium" style={styles.folderName}>
                  {item.name}
                </Text>
                <Text variant="bodySmall" style={styles.folderCount}>
                  {noteCount} note{noteCount !== 1 ? 's' : ''}
                </Text>
              </View>
            </View>

            <Menu
              visible={folderMenuVisible === item.id}
              onDismiss={() => setFolderMenuVisible(null)}
              anchor={
                <IconButton
                  icon="dots-vertical"
                  onPress={() => setFolderMenuVisible(item.id)}
                  style={styles.folderMenuButton}
                />
              }
              contentStyle={[
                styles.menuContent,
                { backgroundColor: isDark ? Colors.dark.surface : Colors.light.surface }
              ]}
              anchorPosition="bottom"
            >
              <Menu.Item
                title="Rename"
                leadingIcon="pencil"
                onPress={() => openEditDialog(item)}
              />
              <Divider />
              <Menu.Item
                title="Delete"
                leadingIcon="delete"
                onPress={() => handleDeleteFolder(item)}
              />
            </Menu>
          </View>

          <Text variant="bodySmall" style={styles.folderDate}>
            Created {new Date(item.created_at).toLocaleDateString()}
          </Text>
        </Card.Content>
      </Card>
    );
  };


  // Filter notes by folder if selected
  const displayNotes = selectedFolderId
    ? notes.filter(note => note.folder_id === selectedFolderId)
    : notes;

  // Enhanced toggle component with icons matching the design
  const renderToggle = () => {
    const currentColors = isDark ? Colors.dark : Colors.light;

    return (
      <View style={[
        styles.toggleContainer,
        { backgroundColor: currentColors.surfaceVariant }
      ]}>
        <TouchableOpacity
          style={[
            styles.toggleButton,
            currentView === 'notes' && [
              styles.toggleButtonActive,
              { backgroundColor: currentColors.primary }
            ]
          ]}
          onPress={() => setCurrentView('notes')}
          accessibilityRole="button"
          accessibilityLabel="Switch to Notes view"
          accessibilityState={{ selected: currentView === 'notes' }}
          accessibilityHint="Tap to view your notes"
        >
          <IconButton
            icon="pencil"
            size={18}
            iconColor={currentView === 'notes' ? currentColors.onPrimary : currentColors.onSurfaceVariant}
            style={styles.toggleIcon}
          />
          <Text style={[
            styles.toggleText,
            { color: currentView === 'notes' ? currentColors.onPrimary : currentColors.onSurfaceVariant }
          ]}>
            Notes
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.toggleButton,
            currentView === 'folders' && [
              styles.toggleButtonActive,
              { backgroundColor: currentColors.primary }
            ]
          ]}
          onPress={() => setCurrentView('folders')}
          accessibilityRole="button"
          accessibilityLabel="Switch to Folders view"
          accessibilityState={{ selected: currentView === 'folders' }}
          accessibilityHint="Tap to view your folders"
        >
          <IconButton
            icon="folder"
            size={18}
            iconColor={currentView === 'folders' ? currentColors.onPrimary : currentColors.onSurfaceVariant}
            style={styles.toggleIcon}
          />
          <Text style={[
            styles.toggleText,
            { color: currentView === 'folders' ? currentColors.onPrimary : currentColors.onSurfaceVariant }
          ]}>
            Folders
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: isDark ? Colors.dark.background : Colors.light.background }]}>
      <Appbar.Header style={{ backgroundColor: isDark ? Colors.dark.surface : Colors.light.surface, elevation: 0 }}>
        <Appbar.Content
          title="SuperNote"
          titleStyle={[styles.headerTitle, { color: isDark ? Colors.dark.onSurface : Colors.light.onSurface }]}
        />
        <IconButton
          icon="magnify"
          size={24}
          iconColor={isDark ? Colors.dark.onSurface : Colors.light.onSurface}
          onPress={() => router.push('/(tabs)/search')}
          style={styles.headerIcon}
        />
        <IconButton
          icon="cog"
          size={24}
          iconColor={isDark ? Colors.dark.onSurface : Colors.light.onSurface}
          onPress={() => router.push('/settings')}
          style={styles.headerIcon}
        />
      </Appbar.Header>

      {/* Enhanced Toggle */}
      {renderToggle()}

      <View style={styles.content}>
        {/* Database Status Banner */}
        {databaseError && (
          <Banner
            visible={true}
            actions={[
              {
                label: 'Retry',
                onPress: testDatabaseConnection,
              },
              {
                label: 'Setup Guide',
                onPress: () => Alert.alert(
                  'Database Setup Required',
                  'Please run the database setup script in your Supabase dashboard. Check DATABASE_SETUP.md for instructions.',
                  [{ text: 'OK' }]
                ),
              },
            ]}
            icon="database-alert"
          >
            Database Error: {databaseError}
          </Banner>
        )}

        {!databaseReady && !databaseError && user && (
          <Banner
            visible={true}
            actions={[
              {
                label: 'Test Connection',
                onPress: testDatabaseConnection,
              },
            ]}
            icon="database-sync"
          >
            Setting up database connection...
          </Banner>
        )}

        {/* Content based on current view */}
        {currentView === 'notes' ? (
          // Notes View
          <>
            {/* Section Header */}
            <View style={[
              styles.sectionHeader,
              { borderBottomColor: isDark ? Colors.dark.outline : Colors.light.outline }
            ]}>
              <Text variant="headlineSmall" style={[styles.sectionTitle, { color: isDark ? Colors.dark.onBackground : Colors.light.onBackground }]}>
                Notes
              </Text>
              <Text variant="bodyMedium" style={[styles.sectionSubtitle, { color: isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant }]}>
                {notes.length} {notes.length === 1 ? 'note' : 'notes'}
              </Text>
            </View>

            {databaseReady && notes.length === 0 && !isLoading && (
              <View style={styles.emptyStateContainer}>
                <Text style={styles.emptyStateEmoji}>🤔</Text>
                <Text variant="bodyMedium" style={styles.emptyStateText}>
                  You haven&apos;t created any notes yet.
                </Text>
              </View>
            )}

            {isLoading && !refreshing ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" />
                <Text style={styles.loadingText}>Loading notes...</Text>
              </View>
            ) : (
              <FlatList
                data={displayNotes}
                renderItem={renderNoteItem}
                keyExtractor={(item) => item.id}
                contentContainerStyle={styles.notesList}
                refreshControl={
                  <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
                }
                showsVerticalScrollIndicator={false}
              />
            )}
          </>
        ) : (
          // Folders View
          <>
            {folders.length === 0 ? (
              <View style={styles.emptyStateContainer}>
                <Text style={styles.emptyStateEmoji}>📁</Text>
                <Text variant="bodyMedium" style={styles.emptyStateText}>
                  No folders yet. Create one to organize your notes.
                </Text>
                <Button
                  mode="contained"
                  onPress={openCreateDialog}
                  style={styles.emptyStateButton}
                  icon="folder-plus"
                >
                  Create Your First Folder
                </Button>
              </View>
            ) : (
              <>
                <FlatList
                  data={folders}
                  keyExtractor={(item) => item.id}
                  renderItem={renderFolder}
                  contentContainerStyle={styles.foldersList}
                  showsVerticalScrollIndicator={false}
                />
                <View style={styles.newFolderButtonContainer}>
                  <Button
                    mode="contained"
                    onPress={openCreateDialog}
                    style={styles.newFolderButton}
                    icon="folder-plus"
                  >
                    New Folder
                  </Button>
                </View>
              </>
            )}
          </>
        )}
      </View>

      {/* Floating Action Button - Only show in Notes view */}
      {currentView === 'notes' && (
        <TouchableOpacity
          style={styles.floatingButton}
          onPress={handleCreateNote}
          accessibilityLabel="Create new note"
          accessibilityHint="Tap to create a new note"
        >
          <IconButton
            icon="plus"
            size={24}
            iconColor="#fff"
            style={styles.floatingButtonIcon}
          />
        </TouchableOpacity>
      )}

      {/* Create/Edit Folder Dialog */}
      <Portal>
        <Dialog
          visible={dialogVisible}
          onDismiss={() => setDialogVisible(false)}
          style={styles.dialog}
        >
          <Dialog.Title style={[
            styles.dialogTitle,
            { color: isDark ? Colors.dark.onSurface : Colors.light.onSurface }
          ]}>
            {editingFolder ? 'Rename Folder' : 'Create New Folder'}
          </Dialog.Title>
          <Dialog.Content style={styles.dialogContent}>
            <TextInput
              label="Folder Name"
              value={folderName}
              onChangeText={setFolderName}
              mode="outlined"
              placeholder="Enter folder name..."
              autoFocus
              onSubmitEditing={editingFolder ? handleEditFolder : handleCreateFolder}
              accessibilityLabel={editingFolder ? "Edit folder name" : "Enter new folder name"}
              accessibilityHint={editingFolder ? "Enter the new name for this folder" : "Enter a name for your new folder"}
              style={styles.dialogInput}
            />
          </Dialog.Content>
          <Dialog.Actions style={styles.dialogActions}>
            <Button
              onPress={() => setDialogVisible(false)}
              textColor={isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant}
            >
              Cancel
            </Button>
            <Button
              onPress={editingFolder ? handleEditFolder : handleCreateFolder}
              disabled={!folderName.trim() || foldersLoading}
              loading={foldersLoading}
              mode="contained"
              style={styles.dialogPrimaryButton}
            >
              {editingFolder ? 'Rename' : 'Create'}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },

  // Header styles
  headerTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
  },
  headerIcon: {
    margin: 0,
  },

  // Enhanced toggle styles matching the design
  toggleContainer: {
    flexDirection: 'row',
    borderRadius: BorderRadius.round, // More rounded like in screenshot
    marginHorizontal: Spacing.lg,
    marginTop: Spacing.lg,
    marginBottom: Spacing.md,
    padding: 4, // Reduced padding for more compact look
    ...Shadows.md,
    elevation: 3,
  },

  // Section header styles
  sectionHeader: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.outline, // Will be overridden by theme
  },
  sectionTitle: {
    fontWeight: Typography.fontWeight.bold,
    marginBottom: Spacing.xs,
    fontSize: Typography.fontSize.lg,
  },
  sectionSubtitle: {
    fontSize: Typography.fontSize.sm,
    opacity: 0.8,
    fontWeight: Typography.fontWeight.medium,
  },
  toggleButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10, // More compact vertical padding
    paddingHorizontal: Spacing.md,
    borderRadius: 40, // More rounded to match pill shape
    gap: Spacing.xs,
  },
  toggleButtonActive: {
    // Will be set dynamically
  },
  toggleIcon: {
    margin: 0,
  },
  toggleText: {
    fontSize: Typography.fontSize.md, // Increased from sm to md
    fontWeight: Typography.fontWeight.bold, // Made bold
  },
  toggleTextActive: {
    // Will be set dynamically
  },



  // Enhanced notes styles
  notesList: {
    padding: Spacing.lg,
    paddingTop: Spacing.xs, // Reduced whitespace
    paddingBottom: Spacing.xl, // Reduced space for floating button
  },
  noteCard: {
    marginBottom: Spacing.lg,
    borderRadius: BorderRadius.xl,
    ...Shadows.md,
    overflow: 'hidden',
  },
  noteCardContent: {
    padding: Spacing.xl,
  },
  noteHeader: {
    marginBottom: Spacing.xs,
  },
  noteActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
  },
  spacer: {
    height: Spacing.xs,
  },
  floatingButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.light.primary,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  floatingButtonIcon: {
    margin: 0,
  },
  noteTitle: {
    flex: 1,
    fontWeight: Typography.fontWeight.semibold,
    fontSize: Typography.fontSize.lg,
    lineHeight: Typography.lineHeight.lg,
  },
  pinIcon: {
    margin: 0,
  },
  noteMenuButton: {
    margin: 0,
  },
  noteContent: {
    lineHeight: Typography.lineHeight.lg,
    fontSize: Typography.fontSize.md,
    marginBottom: Spacing.md,
  },
  noteFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: Spacing.sm,
  },
  noteTags: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  tag: {
    marginRight: 4,
    height: 24,
  },
  moreTagsText: {
    fontSize: 12,
    opacity: 0.6,
    marginLeft: 4,
  },
  noteDate: {
    opacity: 0.6,
  },

  // Folders styles
  foldersList: {
    padding: Spacing.lg,
    paddingBottom: Spacing.xl, // Reduced space for floating button
  },
  folderCard: {
    marginBottom: Spacing.lg,
    borderRadius: BorderRadius.xl,
    ...Shadows.md,
    overflow: 'hidden',
  },
  folderHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  folderInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  folderIcon: {
    margin: 0,
    marginRight: 8,
  },
  folderDetails: {
    flex: 1,
  },
  folderName: {
    fontWeight: '600',
    marginBottom: 2,
  },
  folderCount: {
    opacity: 0.7,
  },
  folderDate: {
    opacity: 0.5,
  },
  folderMenuButton: {
    margin: 0,
  },

  // Menu styles
  menuContent: {
    borderRadius: BorderRadius.lg,
    ...Shadows.md,
    elevation: 8,
    marginTop: 4,
  },

  // Dialog styles
  dialog: {
    borderRadius: BorderRadius.xl,
    ...Shadows.lg,
    margin: Spacing.lg,
  },
  dialogTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.semibold,
    paddingBottom: Spacing.sm,
  },
  dialogContent: {
    paddingVertical: Spacing.md,
  },
  dialogInput: {
    marginBottom: Spacing.sm,
  },
  dialogActions: {
    paddingTop: Spacing.md,
    gap: Spacing.sm,
  },
  dialogPrimaryButton: {
    minWidth: 100,
  },

  // Loading and empty states
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    opacity: 0.6,
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing.xl,
    paddingTop: Spacing.xxxxxxl,
  },
  emptyStateEmoji: {
    fontSize: 64,
    marginBottom: Spacing.xl,
  },
  emptyStateText: {
    textAlign: 'center',
    opacity: 0.7,
    marginBottom: Spacing.xl,
    fontSize: Typography.fontSize.lg,
    lineHeight: Typography.lineHeight.xl,
    fontWeight: Typography.fontWeight.medium,
  },
  emptyStateButton: {
    minWidth: 200,
    borderRadius: BorderRadius.xl,
    paddingVertical: Spacing.sm,
  },
  newFolderButtonContainer: {
    padding: Spacing.lg,
    paddingTop: Spacing.md,
  },
  newFolderButton: {
    width: '100%',
    borderRadius: BorderRadius.xl,
    paddingVertical: Spacing.sm,
  },


});
