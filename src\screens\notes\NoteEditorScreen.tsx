import { BorderRadius, Colors, Shadows, Spacing, Typography } from '@/constants/theme';
import { useColorScheme } from '@/hooks/use-color-scheme';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  View
} from 'react-native';
import {
  ActivityIndicator,
  Appbar,
  Button,
  Chip,
  Dialog,
  Divider,
  Menu,
  Portal,
  Text,
  TextInput
} from 'react-native-paper';
import AISummarizeButton from '../../components/ai/AISummarizeButton';
import VoiceRecorder from '../../components/ai/VoiceRecorder';
import { useAuthStore } from '../../store/authStore';
import { useFoldersStore } from '../../store/foldersStore';
import { useNotesStore } from '../../store/notesStore';
import { NoteForm } from '../../types';

export default function NoteEditorScreen() {
  const router = useRouter();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { noteId } = useLocalSearchParams<{ noteId?: string }>();
  const { user } = useAuthStore();
  const {
    currentNote,
    createNote,
    updateNote,
    loadNote,
    isLoading
  } = useNotesStore();
  const { folders } = useFoldersStore();



  const [form, setForm] = useState<NoteForm>({
    title: '',
    content: '',
    folderId: undefined,
    tags: [],
    isPinned: false,
  });

  const [menuVisible, setMenuVisible] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [folderDialogVisible, setFolderDialogVisible] = useState(false);
  const [tagDialogVisible, setTagDialogVisible] = useState(false);
  const [newTag, setNewTag] = useState('');

  useEffect(() => {
    if (noteId && noteId !== 'new') {
      loadNote(noteId);
    }
  }, [noteId, loadNote]);

  useEffect(() => {
    if (currentNote) {
      setForm({
        title: currentNote.title,
        content: currentNote.content,
        folderId: currentNote.folder_id,
        tags: currentNote.tags,
        isPinned: currentNote.is_pinned,
      });
    }
  }, [currentNote]);

  const handleSave = async () => {
    if (!user) {
      Alert.alert('Error', 'You must be logged in to save notes.');
      return;
    }

    try {
      let success = false;

      if (noteId && noteId !== 'new' && currentNote) {
        console.log('Updating existing note:', currentNote.id);
        // Update existing note
        success = await updateNote(currentNote.id, {
          title: form.title || 'Untitled',
          content: form.content,
          tags: form.tags,
          is_pinned: form.isPinned,
        });
      } else {
        // Create new note
        success = await createNote(form, user.id);
      }

      if (success) {
        setHasChanges(false);
        router.back();
      } else {
        Alert.alert('Error', 'Failed to save note. Please try again.');
      }
    } catch (error) {
      console.error('Error saving note:', error);
      Alert.alert('Error', 'An unexpected error occurred while saving.');
    }
  };

  const handleDiscard = () => {
    if (hasChanges) {
      Alert.alert(
        'Discard Changes',
        'Are you sure you want to discard your changes?',
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Discard', 
            style: 'destructive',
            onPress: () => router.back()
          },
        ]
      );
    } else {
      router.back();
    }
  };

  const handleFormChange = (field: keyof NoteForm, value: any) => {
    setForm(prev => ({ ...prev, [field]: value }));
    setHasChanges(true);
  };

  const togglePin = () => {
    handleFormChange('isPinned', !form.isPinned);
  };

  const handleSummaryGenerated = async (summary: string) => {
    if (!user) return;

    // Create a new note with the summary
    const summaryNote = {
      title: `Summary: ${form.title || 'Untitled Note'}`,
      content: summary,
      folderId: form.folderId,
      tags: [...form.tags, 'AI Summary'],
      isPinned: false,
    };

    const success = await createNote(summaryNote, user.id);
    if (success) {
      Alert.alert('Success', 'Summary note created successfully!');
    }
  };

  const handleTranscriptionComplete = (transcription: string) => {
    // Append transcription to current note content
    const newContent = form.content
      ? `${form.content}\n\n--- Voice Note ---\n${transcription}`
      : transcription;

    setForm(prev => ({ ...prev, content: newContent }));
    setHasChanges(true);
  };

  return (
    <View style={[styles.container, { backgroundColor: isDark ? Colors.dark.background : Colors.light.background }]}>
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Modern Minimal Header */}
        <Appbar.Header style={{ backgroundColor: 'transparent', elevation: 0 }}>
          <Appbar.BackAction
            onPress={handleDiscard}
            iconColor={isDark ? Colors.dark.onBackground : Colors.light.onBackground}
          />

          {/* Compact Action Bar */}
          <View style={styles.actionBar}>
            <Appbar.Action
              icon={form.isPinned ? 'pin' : 'pin-outline'}
              onPress={togglePin}
              iconColor={form.isPinned ? (isDark ? Colors.dark.primary : Colors.light.primary) : (isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant)}
              size={20}
            />
            <VoiceRecorder
              onTranscriptionComplete={handleTranscriptionComplete}
            />
            <AISummarizeButton
              noteContent={form.content}
              onSummaryGenerated={handleSummaryGenerated}
            />
            <Menu
              visible={menuVisible}
              onDismiss={() => setMenuVisible(false)}
              anchor={
                <Appbar.Action
                  icon="dots-vertical"
                  onPress={() => setMenuVisible(true)}
                  iconColor={isDark ? Colors.dark.onBackground : Colors.light.onBackground}
                  size={20}
                />
              }
              contentStyle={{ backgroundColor: isDark ? Colors.dark.surface : Colors.light.surface }}
            >
              <Menu.Item
                onPress={() => {
                  setMenuVisible(false);
                  setFolderDialogVisible(true);
                }}
                title="Add to Folder"
                leadingIcon="folder-plus"
                titleStyle={{ color: isDark ? Colors.dark.onSurface : Colors.light.onSurface }}
              />
              <Menu.Item
                onPress={() => {
                  setMenuVisible(false);
                  setTagDialogVisible(true);
                }}
                title="Add Tags"
                leadingIcon="tag-plus"
                titleStyle={{ color: isDark ? Colors.dark.onSurface : Colors.light.onSurface }}
              />
              <Divider style={{ backgroundColor: isDark ? Colors.dark.outline : Colors.light.outline }} />
              <Menu.Item
                onPress={handleSave}
                title="Save Note"
                leadingIcon="content-save"
                titleStyle={{ color: isDark ? Colors.dark.primary : Colors.light.primary }}
                disabled={isLoading || !hasChanges}
              />
            </Menu>
          </View>
        </Appbar.Header>

        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={isDark ? Colors.dark.primary : Colors.light.primary} />
          </View>
        ) : (
          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {/* Google Keep Style Note Card */}
            <View
              style={{
                ...styles.noteCard,
                backgroundColor: isDark ? Colors.dark.surface : Colors.light.surface,
                margin: Spacing.lg,
                borderRadius: BorderRadius.xl,
              }}
            >
              <View style={{ padding: Spacing.lg }}>
                {/* Title Input - Clean and minimal */}
                <TextInput
                  value={form.title}
                  onChangeText={(text) => handleFormChange('title', text)}
                  mode="flat"
                  style={styles.titleInput}
                  placeholder="Title"
                  underlineColor="transparent"
                  activeUnderlineColor="transparent"
                  textColor={isDark ? Colors.dark.onSurface : Colors.light.onSurface}
                  placeholderTextColor={isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant}
                  contentStyle={{
                    fontSize: Typography.fontSize.xl,
                    fontWeight: Typography.fontWeight.semibold,
                    paddingHorizontal: 0,
                  }}
                />

                {/* Content Input - Seamless integration */}
                <TextInput
                  value={form.content}
                  onChangeText={(text) => handleFormChange('content', text)}
                  mode="flat"
                  multiline
                  style={styles.contentInput}
                  placeholder="Take a note..."
                  underlineColor="transparent"
                  activeUnderlineColor="transparent"
                  textColor={isDark ? Colors.dark.onSurface : Colors.light.onSurface}
                  placeholderTextColor={isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant}
                  contentStyle={{
                    fontSize: Typography.fontSize.md,
                    lineHeight: Typography.lineHeight.md,
                    paddingHorizontal: 0,
                    paddingTop: Spacing.sm,
                  }}
                />

                {/* Tags Display - Compact and clean */}
                {form.tags.length > 0 && (
                  <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    style={styles.tagsContainer}
                    contentContainerStyle={{ paddingTop: Spacing.md }}
                  >
                    {form.tags.map((tag, index) => (
                      <Chip
                        key={index}
                        mode="outlined"
                        style={[
                          styles.tagChip,
                          {
                            backgroundColor: 'transparent',
                            borderColor: isDark ? Colors.dark.outline : Colors.light.outline,
                            marginRight: Spacing.xs,
                          }
                        ]}
                        textStyle={{
                          color: isDark ? Colors.dark.onSurface : Colors.light.onSurface,
                          fontSize: Typography.fontSize.sm
                        }}
                        onClose={() => {
                          const newTags = form.tags.filter((_, i) => i !== index);
                          handleFormChange('tags', newTags);
                        }}
                      >
                        {tag}
                      </Chip>
                    ))}
                  </ScrollView>
                )}
              </View>
            </View>

            {/* Enhanced Save Prompt */}
            {hasChanges && (
              <View style={{ ...styles.savePrompt, backgroundColor: isDark ? Colors.dark.primaryContainer : Colors.light.primaryContainer }}>
                <View style={styles.savePromptContent}>
                  <View>
                    <Text
                      variant="titleSmall"
                      style={{ color: isDark ? Colors.dark.onPrimaryContainer : Colors.light.onPrimaryContainer }}
                    >
                      Unsaved Changes
                    </Text>
                    <Text
                      variant="bodySmall"
                      style={{ color: isDark ? Colors.dark.onPrimaryContainer : Colors.light.onPrimaryContainer, opacity: 0.8 }}
                    >
                      Your changes will be lost if you don&apos;t save them
                    </Text>
                  </View>
                  <Button
                    mode="contained"
                    onPress={handleSave}
                    loading={isLoading}
                    disabled={isLoading}
                    style={[styles.saveButton, { backgroundColor: isDark ? Colors.dark.primary : Colors.light.primary }]}
                    labelStyle={{ color: isDark ? Colors.dark.onPrimary : Colors.light.onPrimary }}
                  >
                    Save Changes
                  </Button>
                </View>
              </View>
            )}
          </ScrollView>
        )}

      {/* Folder Selection Dialog */}
      <Portal>
        <Dialog visible={folderDialogVisible} onDismiss={() => setFolderDialogVisible(false)}>
          <Dialog.Title>Select Folder</Dialog.Title>
          <Dialog.Content>
            <ScrollView style={styles.dialogContent}>
              <Menu.Item
                title="No Folder"
                onPress={() => {
                  handleFormChange('folderId', undefined);
                  setFolderDialogVisible(false);
                }}
                leadingIcon={!form.folderId ? 'check' : 'folder-outline'}
              />
              {folders.map((folder) => (
                <Menu.Item
                  key={folder.id}
                  title={folder.name}
                  onPress={() => {
                    handleFormChange('folderId', folder.id);
                    setFolderDialogVisible(false);
                  }}
                  leadingIcon={form.folderId === folder.id ? 'check' : 'folder'}
                />
              ))}
            </ScrollView>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setFolderDialogVisible(false)}>Cancel</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      {/* Tag Addition Dialog */}
      <Portal>
        <Dialog visible={tagDialogVisible} onDismiss={() => setTagDialogVisible(false)}>
          <Dialog.Title>Add Tag</Dialog.Title>
          <Dialog.Content>
            <TextInput
              label="Tag Name"
              value={newTag}
              onChangeText={setNewTag}
              mode="outlined"
              placeholder="Enter tag name..."
              onSubmitEditing={() => {
                if (newTag.trim() && !form.tags.includes(newTag.trim())) {
                  handleFormChange('tags', [...form.tags, newTag.trim()]);
                  setNewTag('');
                  setTagDialogVisible(false);
                }
              }}
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => {
              setNewTag('');
              setTagDialogVisible(false);
            }}>Cancel</Button>
            <Button
              onPress={() => {
                if (newTag.trim() && !form.tags.includes(newTag.trim())) {
                  handleFormChange('tags', [...form.tags, newTag.trim()]);
                  setNewTag('');
                  setTagDialogVisible(false);
                }
              }}
              disabled={!newTag.trim() || form.tags.includes(newTag.trim())}
            >
              Add
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingBottom: Spacing.xl,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionBar: {
    flex: 1,
    justifyContent: 'flex-end',
    paddingRight: Spacing.sm,
    flexDirection: 'row',
    gap: Spacing.xs,
  },
  savePromptContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: Spacing.md,
  },

  // Google Keep style note card
  noteCard: {
    borderRadius: BorderRadius.xl,
    ...Shadows.md,
    marginHorizontal: Spacing.md,
  },

  // Clean input styles
  titleInput: {
    backgroundColor: 'transparent',
    marginBottom: 0,
  },
  contentInput: {
    backgroundColor: 'transparent',
    minHeight: 200,
    textAlignVertical: 'top',
  },

  // Compact tags
  tagsContainer: {
    flexDirection: 'row',
  },
  tagChip: {
    height: 28,
    borderRadius: BorderRadius.round,
  },

  // Dialog styles
  savePrompt: {
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    ...Shadows.md,
  },
  saveButton: {
    borderRadius: BorderRadius.lg,
    paddingVertical: Spacing.xs,
  },
  dialogContent: {
    maxHeight: 300,
  },
});
