import { BorderRadius, Colors, Shadows, Spacing, Typography } from '@/constants/theme';
import { useColorScheme } from '@/hooks/use-color-scheme';
import { MaterialIcons } from '@expo/vector-icons';
import React, { useRef, useState } from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { Button, IconButton, Text } from 'react-native-paper';
import {
  useSharedValue
} from 'react-native-reanimated';



interface OnboardingSlide {
  id: number;
  title: string;
  description: string;
  icon: keyof typeof MaterialIcons.glyphMap;
  color: string;
}

const slides: OnboardingSlide[] = [
  {
    id: 1,
    title: 'Welcome to SuperNote',
    description: 'Your intelligent note-taking companion that helps you capture, organize, and find your thoughts effortlessly.',
    icon: 'note-add',
    color: '#6200EE', // Using primary color from theme
  },
  {
    id: 2,
    title: 'AI-Powered Organization',
    description: 'Let our AI help you categorize, tag, and organize your notes automatically for better productivity.',
    icon: 'auto-awesome',
    color: '#006D3B', // Using success color from theme
  },
  {
    id: 3,
    title: 'Sync Across Devices',
    description: 'Access your notes anywhere, anytime. Your data is securely synced across all your devices.',
    icon: 'sync',
    color: '#7D5700', // Using warning color from theme
  },
  {
    id: 4,
    title: 'Ready to Start?',
    description: 'Join thousands of users who have transformed their note-taking experience with SuperNote.',
    icon: 'rocket-launch',
    color: '#9B59B6', // Using a complementary purple color
  },
];

interface OnboardingScreenProps {
  onComplete: () => void;
}

export default function OnboardingScreen({ onComplete }: OnboardingScreenProps) {
  const colorScheme = useColorScheme();
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollViewRef = useRef<ScrollView>(null);
  const scrollX = useSharedValue(0);

  const isDark = colorScheme === 'dark';
  const slideWidth = 400; // Fixed width for slides

  const handleNext = () => {
    if (currentIndex < slides.length - 1) {
      const nextIndex = currentIndex + 1;
      setCurrentIndex(nextIndex);
      scrollViewRef.current?.scrollTo({
        x: nextIndex * slideWidth,
        animated: true,
      });
    } else {
      onComplete();
    }
  };

  const handlePrevious = () => {
    if (currentIndex > 0) {
      const prevIndex = currentIndex - 1;
      setCurrentIndex(prevIndex);
      scrollViewRef.current?.scrollTo({
        x: prevIndex * slideWidth,
        animated: true,
      });
    }
  };

  const handleSkip = () => {
    onComplete();
  };

  const onScroll = (event: any) => {
    const offsetX = event.nativeEvent.contentOffset.x;
    scrollX.value = offsetX;
    const index = Math.round(offsetX / slideWidth);
    setCurrentIndex(index);
  };

  const renderSlide = (slide: OnboardingSlide, index: number) => {
    const iconSize = 80; // Fixed size
    const containerSize = 150; // Fixed size

    return (
      <View key={slide.id} style={[styles.slide, { width: slideWidth }]}>
        <View style={styles.slideContent}>
          <View style={styles.slideColumn}>
            {/* Enhanced Icon */}
            <View style={[
              styles.iconContainer,
              {
                backgroundColor: slide.color,
                width: containerSize,
                height: containerSize,
                borderRadius: containerSize / 2,
                ...Shadows.lg
              }
            ]}>
              <MaterialIcons name={slide.icon} size={iconSize} color={isDark ? Colors.dark.onPrimary : Colors.light.onPrimary} />
            </View>

            {/* Enhanced Title */}
            <Text style={[
              styles.title,
              {
                color: isDark ? Colors.dark.onBackground : Colors.light.onBackground,
                fontSize: Typography.fontSize.xxl,
                fontWeight: Typography.fontWeight.bold,
                textAlign: 'center'
              }
            ]}>
              {slide.title}
            </Text>

            {/* Enhanced Description */}
            <Text style={[
              styles.description,
              {
                color: isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant,
                fontSize: Typography.fontSize.lg,
                lineHeight: Typography.lineHeight.lg,
                textAlign: 'center',
                maxWidth: 400
              }
            ]}>
              {slide.description}
            </Text>
          </View>
        </View>
      </View>
    );
  };

  const renderPagination = () => {
    return (
      <View style={styles.pagination}>
        {slides.map((_, index) => {
          const isActive = index === currentIndex;
          return (
            <View
              key={index}
              style={[
                styles.paginationDot,
                {
                  backgroundColor: isDark ? Colors.dark.primary : Colors.light.primary,
                  opacity: isActive ? 1 : 0.3,
                  transform: [{ scale: isActive ? 1.3 : 1 }],
                  width: isActive ? 24 : 8,
                  borderRadius: isActive ? 12 : 4,
                },
              ]}
            />
          );
        })}
      </View>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: isDark ? Colors.dark.background : Colors.light.background }]}>
      {/* Enhanced Skip Button */}
      <View style={styles.header}>
        <Button
          mode="text"
          onPress={handleSkip}
          textColor={isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant}
          labelStyle={{ fontSize: Typography.fontSize.md, fontWeight: Typography.fontWeight.medium }}
        >
          Skip
        </Button>
      </View>

      {/* Slides */}
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={onScroll}
        scrollEventThrottle={16}
        style={styles.scrollView}
      >
        {slides.map((slide, index) => renderSlide(slide, index))}
      </ScrollView>

      {/* Pagination */}
      {renderPagination()}

      {/* Enhanced Navigation */}
      <View style={styles.navigation}>
        <IconButton
          icon="chevron-left"
          size={28}
          onPress={handlePrevious}
          disabled={currentIndex === 0}
          iconColor={currentIndex === 0 ? (isDark ? Colors.dark.outline : Colors.light.outline) : (isDark ? Colors.dark.onSurface : Colors.light.onSurface)}
          style={[styles.navButton, { backgroundColor: isDark ? Colors.dark.surfaceVariant : Colors.light.surfaceVariant }]}
        />

        <Button
          mode="contained"
          onPress={handleNext}
          style={[
            styles.nextButton,
            {
              backgroundColor: slides[currentIndex].color,
              borderRadius: BorderRadius.xl,
              ...Shadows.md
            }
          ]}
          contentStyle={styles.nextButtonContent}
          labelStyle={{
            fontSize: Typography.fontSize.md,
            fontWeight: Typography.fontWeight.semibold,
            color: isDark ? Colors.dark.onPrimary : Colors.light.onPrimary
          }}
        >
          {currentIndex === slides.length - 1 ? 'Get Started' : 'Next'}
        </Button>

        <IconButton
          icon="chevron-right"
          size={28}
          onPress={handleNext}
          disabled={currentIndex === slides.length - 1}
          iconColor={currentIndex === slides.length - 1 ? (isDark ? Colors.dark.outline : Colors.light.outline) : (isDark ? Colors.dark.onSurface : Colors.light.onSurface)}
          style={[styles.navButton, { backgroundColor: isDark ? Colors.dark.surfaceVariant : Colors.light.surfaceVariant }]}
        />
    </View>

    <View style={styles.spacer} />
  </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  scrollView: {
    flex: 1,
  },
  slide: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  slideContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  slideColumn: {
    alignItems: 'center',
    gap: Spacing.xl,
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    textAlign: 'center',
  },
  description: {
    textAlign: 'center',
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  paginationDot: {
    height: 8,
    marginHorizontal: Spacing.xs,
  },
  navigation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  navButton: {
    borderRadius: BorderRadius.lg,
    ...Shadows.sm,
  },
  nextButton: {
    minWidth: 140,
    borderRadius: BorderRadius.xl,
  },
  nextButtonContent: {
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
  },
  spacer: {
    height: Spacing.lg,
  },
});
