import { BorderRadius, Colors, Shadows, Spacing, Typography } from '@/constants/theme';
import { useManualTheme } from '@/hooks/use-manual-theme';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Al<PERSON>, ScrollView, StyleSheet, View } from 'react-native';
import {
  Appbar,
  Avatar,
  Button,
  Divider,
  List,
  Menu,
  Switch,
  Text
} from 'react-native-paper';
import { useAuthStore } from '../../store/authStore';

export default function SettingsScreen() {
  const router = useRouter();
  const { colorScheme, setThemeMode } = useManualTheme();
  const isDark = (colorScheme as 'light' | 'dark') === 'dark';
  const { user, signOut } = useAuthStore();

  // State for biometric settings
  const [biometricEnabled, setBiometricEnabled] = useState(false);
  const [biometricAvailable, setBiometricAvailable] = useState(false);

  // State for theme menu
  const [themeMenuVisible, setThemeMenuVisible] = useState(false);

  useEffect(() => {
    // Biometric functionality disabled
    setBiometricAvailable(false);
    setBiometricEnabled(false);
  }, []);

  const handleBiometricToggle = async (enabled: boolean) => {
    // Biometric functionality disabled
    Alert.alert('Info', 'Biometric authentication is not available in this version.');
  };

  const handleThemeChange = (newTheme: 'light') => {
    setThemeMode(newTheme);
    setThemeMenuVisible(false);
  };

  const getThemeDisplayText = () => {
    return 'Light';
  };

  const handleSignOut = async () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            await signOut();
            router.replace('/auth/login');
          }
        },
      ]
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: isDark ? Colors.dark.background : Colors.light.background }]}>
      <Appbar.Header style={{ backgroundColor: isDark ? Colors.dark.surface : Colors.light.surface, elevation: 0 }}>
        <Appbar.BackAction
          onPress={() => router.back()}
        />
        <Appbar.Content
          title="Settings"
          titleStyle={{
            color: isDark ? Colors.dark.onSurface : Colors.light.onSurface,
            fontSize: Typography.fontSize.xl,
            fontWeight: Typography.fontWeight.semibold
          }}
        />
      </Appbar.Header>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={{ padding: Spacing.lg }}>
          {/* Enhanced User Profile Section */}
          <View style={{ ...styles.profileCard, backgroundColor: isDark ? Colors.dark.surface : Colors.light.surface }}>
            <View style={styles.profileContent}>
              <View style={{ alignItems: 'center' }}>
                <Avatar.Text
                  size={80}
                  label={user?.full_name?.charAt(0) || user?.email?.charAt(0) || 'U'}
                  style={{ backgroundColor: isDark ? Colors.dark.primary : Colors.light.primary }}
                  labelStyle={{ color: isDark ? Colors.dark.onPrimary : Colors.light.onPrimary, fontSize: Typography.fontSize.xxl }}
                />
                <View style={{ alignItems: 'center' }}>
                  <Text
                    variant="headlineSmall"
                    style={{ ...styles.userName, color: isDark ? Colors.dark.onSurface : Colors.light.onSurface }}
                  >
                    {user?.full_name || 'User'}
                  </Text>
                  <Text
                    variant="bodyLarge"
                    style={{ ...styles.email, color: isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant }}
                  >
                    {user?.email}
                  </Text>
                  <View style={{
                    ...styles.subscriptionBadge,
                    backgroundColor: user?.subscription_tier === 'premium'
                      ? (isDark ? Colors.dark.primaryContainer : Colors.light.primaryContainer)
                      : (isDark ? Colors.dark.surfaceVariant : Colors.light.surfaceVariant)
                  }}>
                    <Text
                      variant="labelMedium"
                      style={{
                        ...styles.subscriptionText,
                        color: user?.subscription_tier === 'premium'
                          ? (isDark ? Colors.dark.onPrimaryContainer : Colors.light.onPrimaryContainer)
                          : (isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant)
                      }}
                    >
                      {user?.subscription_tier === 'premium' ? '✨ Premium' : 'Free'} Plan
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          </View>
        </View>

        <View style={{ padding: Spacing.lg }}>
          {/* App Settings Section */}
          <View style={{ marginBottom: Spacing.md }}>
            <Text
              variant="titleMedium"
              style={{ ...styles.sectionTitle, color: isDark ? Colors.dark.onSurface : Colors.light.onSurface }}
            >
              App Settings
            </Text>
            <View style={{ ...styles.settingsCard, backgroundColor: isDark ? Colors.dark.surface : Colors.light.surface }}>
              <List.Item
                title="Theme"
                description="Choose your preferred theme"
                left={(props) => <List.Icon {...props} icon="palette" />}
                right={() => (
                  <Menu
                    visible={themeMenuVisible}
                    onDismiss={() => setThemeMenuVisible(false)}
                    anchor={
                      <Button
                        mode="text"
                        onPress={() => setThemeMenuVisible(true)}
                        textColor={isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant}
                      >
                        {getThemeDisplayText()}
                      </Button>
                    }
                  >
                    <Menu.Item onPress={() => handleThemeChange('light')} title="Light" />
                  </Menu>
                )}
                onPress={() => setThemeMenuVisible(true)}
                titleStyle={{ color: isDark ? Colors.dark.onSurface : Colors.light.onSurface }}
                descriptionStyle={{ color: isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant }}
              />
              <Divider style={{ backgroundColor: isDark ? Colors.dark.outline : Colors.light.outline }} />
              <List.Item
                title="Biometric Lock"
                description={biometricAvailable ? 'Use Biometric to secure your notes' : 'Not available on this device'}
                left={(props) => <List.Icon {...props} icon="fingerprint" />}
                right={() => (
                  <Switch
                    value={biometricEnabled}
                    onValueChange={handleBiometricToggle}
                    disabled={!biometricAvailable}
                  />
                )}
                titleStyle={{ color: isDark ? Colors.dark.onSurface : Colors.light.onSurface }}
                descriptionStyle={{ color: isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant }}
              />
              <Divider style={{ backgroundColor: isDark ? Colors.dark.outline : Colors.light.outline }} />
              <List.Item
                title="Auto Sync"
                description="Automatically sync notes"
                left={(props) => <List.Icon {...props} icon="sync" />}
                right={() => <Switch value={true} onValueChange={() => {}} />}
                titleStyle={{ color: isDark ? Colors.dark.onSurface : Colors.light.onSurface }}
                descriptionStyle={{ color: isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant }}
              />
            </View>
          </View>
        </View>

        <View style={{ padding: Spacing.lg }}>
          {/* AI Features Section */}
          <View style={{ marginBottom: Spacing.md }}>
            <Text
              variant="titleMedium"
              style={{ ...styles.sectionTitle, color: isDark ? Colors.dark.onSurface : Colors.light.onSurface }}
            >
              AI Features
            </Text>
            <View style={{ ...styles.settingsCard, backgroundColor: isDark ? Colors.dark.surface : Colors.light.surface }}>
              <List.Item
                title="AI Summarization"
                description="Enable AI-powered note summaries"
                left={(props) => <List.Icon {...props} icon="brain" />}
                right={() => <Switch value={true} onValueChange={() => {}} />}
                titleStyle={{ color: isDark ? Colors.dark.onSurface : Colors.light.onSurface }}
                descriptionStyle={{ color: isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant }}
              />
              <Divider style={{ backgroundColor: isDark ? Colors.dark.outline : Colors.light.outline }} />
              <List.Item
                title="Voice Transcription"
                description="Convert speech to text"
                left={(props) => <List.Icon {...props} icon="microphone" />}
                right={() => <Switch value={true} onValueChange={() => {}} />}
                titleStyle={{ color: isDark ? Colors.dark.onSurface : Colors.light.onSurface }}
                descriptionStyle={{ color: isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant }}
              />
              <Divider style={{ backgroundColor: isDark ? Colors.dark.outline : Colors.light.outline }} />
              <List.Item
                title="Smart Search"
                description="AI-powered semantic search"
                left={(props) => <List.Icon {...props} icon="magnify" />}
                right={() => <Switch value={true} onValueChange={() => {}} />}
                titleStyle={{ color: isDark ? Colors.dark.onSurface : Colors.light.onSurface }}
                descriptionStyle={{ color: isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant }}
              />
            </View>
          </View>
        </View>

        <View style={{ padding: Spacing.lg }}>
          {/* Subscription Section */}
          <View style={{ marginBottom: Spacing.md }}>
            <Text
              variant="titleMedium"
              style={{ ...styles.sectionTitle, color: isDark ? Colors.dark.onSurface : Colors.light.onSurface }}
            >
              Subscription
            </Text>
            <View style={{ ...styles.settingsCard, backgroundColor: isDark ? Colors.dark.surface : Colors.light.surface }}>
              <List.Item
                title="Current Plan"
                description="Free Plan - Basic features included"
                left={(props) => <List.Icon {...props} icon="account-circle" />}
                right={() => (
                  <Text style={{
                    color: isDark ? Colors.dark.primary : Colors.light.primary,
                    fontWeight: Typography.fontWeight.semibold
                  }}>
                    FREE
                  </Text>
                )}
                titleStyle={{ color: isDark ? Colors.dark.onSurface : Colors.light.onSurface }}
                descriptionStyle={{ color: isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant }}
              />
              <Divider style={{ backgroundColor: isDark ? Colors.dark.outline : Colors.light.outline }} />
              <List.Item
                title="Upgrade to Pro"
                description="Unlimited AI features, advanced search, and more"
                left={(props) => <List.Icon {...props} icon="crown" />}
                right={() => (
                  <Text style={{
                    color: isDark ? Colors.dark.warning : Colors.light.warning,
                    fontWeight: Typography.fontWeight.semibold
                  }}>
                    $9.99/mo
                  </Text>
                )}
                onPress={() => Alert.alert('Coming Soon', 'Pro subscription will be available soon!')}
                titleStyle={{ color: isDark ? Colors.dark.onSurface : Colors.light.onSurface }}
                descriptionStyle={{ color: isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant }}
              />
              <Divider style={{ backgroundColor: isDark ? Colors.dark.outline : Colors.light.outline }} />
              <List.Item
                title="Manage Subscription"
                description="View billing and manage your subscription"
                left={(props) => <List.Icon {...props} icon="credit-card" />}
                onPress={() => Alert.alert('Info', 'No active subscription to manage.')}
                titleStyle={{ color: isDark ? Colors.dark.onSurface : Colors.light.onSurface }}
                descriptionStyle={{ color: isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant }}
              />
            </View>
          </View>
        </View>

        <View style={{ padding: Spacing.lg }}>
          {/* Data & Storage Section */}
          <View style={{ marginBottom: Spacing.md }}>
            <Text
              variant="titleMedium"
              style={{ ...styles.sectionTitle, color: isDark ? Colors.dark.onSurface : Colors.light.onSurface }}
            >
              Data & Storage
            </Text>
            <View style={{ ...styles.settingsCard, backgroundColor: isDark ? Colors.dark.surface : Colors.light.surface }}>
              <List.Item
                title="Export Notes"
                description="Export all notes as PDF or text"
                left={(props) => <List.Icon {...props} icon="export" />}
                onPress={() => {}}
                titleStyle={{ color: isDark ? Colors.dark.onSurface : Colors.light.onSurface }}
                descriptionStyle={{ color: isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant }}
              />
              <Divider style={{ backgroundColor: isDark ? Colors.dark.outline : Colors.light.outline }} />
              <List.Item
                title="Storage Usage"
                description="View storage usage details"
                left={(props) => <List.Icon {...props} icon="harddisk" />}
                onPress={() => {}}
                titleStyle={{ color: isDark ? Colors.dark.onSurface : Colors.light.onSurface }}
                descriptionStyle={{ color: isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant }}
              />
              <Divider style={{ backgroundColor: isDark ? Colors.dark.outline : Colors.light.outline }} />
              <List.Item
                title="Clear Cache"
                description="Clear local cache and temporary files"
                left={(props) => <List.Icon {...props} icon="delete-sweep" />}
                onPress={() => {}}
                titleStyle={{ color: isDark ? Colors.dark.onSurface : Colors.light.onSurface }}
                descriptionStyle={{ color: isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant }}
              />
            </View>
          </View>
        </View>

        <View style={{ padding: Spacing.lg }}>
          {/* Support & About Section */}
          <View style={{ marginBottom: Spacing.md }}>
            <Text
              variant="titleMedium"
              style={{ ...styles.sectionTitle, color: isDark ? Colors.dark.onSurface : Colors.light.onSurface }}
            >
              Support & About
            </Text>
            <View style={{ ...styles.settingsCard, backgroundColor: isDark ? Colors.dark.surface : Colors.light.surface }}>
              <List.Item
                title="Help & Support"
                description="Get help and contact support"
                left={(props) => <List.Icon {...props} icon="help-circle" />}
                onPress={() => {}}
                titleStyle={{ color: isDark ? Colors.dark.onSurface : Colors.light.onSurface }}
                descriptionStyle={{ color: isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant }}
              />
              <Divider style={{ backgroundColor: isDark ? Colors.dark.outline : Colors.light.outline }} />
              <List.Item
                title="Privacy Policy"
                description="Read our privacy policy"
                left={(props) => <List.Icon {...props} icon="shield-account" />}
                onPress={() => {}}
                titleStyle={{ color: isDark ? Colors.dark.onSurface : Colors.light.onSurface }}
                descriptionStyle={{ color: isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant }}
              />
              <Divider style={{ backgroundColor: isDark ? Colors.dark.outline : Colors.light.outline }} />
              <List.Item
                title="Terms of Service"
                description="Read our terms of service"
                left={(props) => <List.Icon {...props} icon="file-document" />}
                onPress={() => {}}
                titleStyle={{ color: isDark ? Colors.dark.onSurface : Colors.light.onSurface }}
                descriptionStyle={{ color: isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant }}
              />
              <Divider style={{ backgroundColor: isDark ? Colors.dark.outline : Colors.light.outline }} />
              <List.Item
                title="About SuperNote"
                description="Version 1.0.0"
                left={(props) => <List.Icon {...props} icon="information" />}
                onPress={() => {}}
                titleStyle={{ color: isDark ? Colors.dark.onSurface : Colors.light.onSurface }}
                descriptionStyle={{ color: isDark ? Colors.dark.onSurfaceVariant : Colors.light.onSurfaceVariant }}
              />
            </View>
          </View>
        </View>

        <View style={{ padding: Spacing.lg }}>
          {/* Account Actions Section */}
          <View style={{ marginBottom: Spacing.md }}>
            <View style={{ ...styles.signOutCard, backgroundColor: isDark ? Colors.dark.errorContainer : Colors.light.errorContainer }}>
              <List.Item
                title="Sign Out"
                description="Sign out of your account"
                left={(props) => <List.Icon {...props} icon="logout" />}
                onPress={handleSignOut}
                titleStyle={{ ...styles.signOutText, color: isDark ? Colors.dark.error : Colors.light.error }}
                descriptionStyle={{ color: isDark ? Colors.dark.onErrorContainer : Colors.light.onErrorContainer }}
              />
            </View>
          </View>
        </View>

        <View style={{ height: Spacing.xxl }} />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  profileCard: {
    borderRadius: BorderRadius.xl,
    ...Shadows.lg,
    marginBottom: Spacing.lg,
  },
  profileContent: {
    padding: Spacing.lg,
  },
  userName: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
  },
  email: {
    fontSize: Typography.fontSize.md,
  },
  subscriptionBadge: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.lg,
    marginTop: Spacing.xs,
  },
  subscriptionText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    marginBottom: Spacing.sm,
  },
  settingsCard: {
    borderRadius: BorderRadius.lg,
    ...Shadows.sm,
  },
  signOutCard: {
    borderRadius: BorderRadius.lg,
    ...Shadows.sm,
  },
  signOutText: {
    fontWeight: Typography.fontWeight.medium,
  },
});
