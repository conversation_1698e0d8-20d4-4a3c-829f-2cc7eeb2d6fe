import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize Gemini AI
const API_KEY = process.env.EXPO_PUBLIC_GEMINI_API_KEY;

if (!API_KEY) {
  console.warn('Gemini API key not found. AI features will be disabled.');
}

const genAI = API_KEY ? new GoogleGenerativeAI(API_KEY) : null;

export interface AIResponse {
  success: boolean;
  content?: string;
  error?: string;
}

export interface AIActionResult {
  success: boolean;
  result?: string;
  actionItems?: string[];
  ideas?: string[];
  error?: string;
}

export class GeminiService {
  private static model = genAI?.getGenerativeModel({ model: 'gemini-2.5-flash-lite' });

  /**
   * Check if AI service is available
   */
  static isAvailable(): boolean {
    return !!genAI && !!this.model;
  }

  /**
   * Summarize text content
   */
  static async summarizeText(text: string): Promise<AIResponse> {
    if (!this.isAvailable()) {
      return {
        success: false,
        error: 'AI service is not available. Please check your API key configuration.'
      };
    }

    try {
      const prompt = `Please provide a concise summary of the following text. Focus on the main points and key information:

${text}

Summary:`;

      const result = await this.model!.generateContent(prompt);
      const response = await result.response;
      const summary = response.text();

      return {
        success: true,
        content: summary.trim()
      };
    } catch (error) {
      console.error('Error summarizing text:', error);
      return {
        success: false,
        error: 'Failed to generate summary. Please try again.'
      };
    }
  }

  /**
   * Improve writing quality
   */
  static async improveWriting(text: string): Promise<AIResponse> {
    if (!this.isAvailable()) {
      return {
        success: false,
        error: 'AI service is not available. Please check your API key configuration.'
      };
    }

    try {
      const prompt = `Please improve the following text by correcting grammar, spelling, and enhancing clarity and tone. Keep the original meaning and style:

${text}

Improved text:`;

      const result = await this.model!.generateContent(prompt);
      const response = await result.response;
      const improvedText = response.text();

      return {
        success: true,
        content: improvedText.trim()
      };
    } catch (error) {
      console.error('Error improving writing:', error);
      return {
        success: false,
        error: 'Failed to improve writing. Please try again.'
      };
    }
  }

  /**
   * Find action items in text
   */
  static async findActionItems(text: string): Promise<AIActionResult> {
    if (!this.isAvailable()) {
      return {
        success: false,
        error: 'AI service is not available. Please check your API key configuration.'
      };
    }

    try {
      const prompt = `Analyze the following text and extract all action items, tasks, or things that need to be done. Return them as a simple list, one item per line:

${text}

Action Items:`;

      const result = await this.model!.generateContent(prompt);
      const response = await result.response;
      const actionItemsText = response.text();

      // Parse the response into an array
      const actionItems = actionItemsText
        .split('\n')
        .map(item => item.trim())
        .filter(item => item.length > 0)
        .map(item => item.replace(/^[-•*]\s*/, '')); // Remove bullet points

      return {
        success: true,
        actionItems
      };
    } catch (error) {
      console.error('Error finding action items:', error);
      return {
        success: false,
        error: 'Failed to find action items. Please try again.'
      };
    }
  }

  /**
   * Explain complex topics
   */
  static async explainText(text: string): Promise<AIResponse> {
    if (!this.isAvailable()) {
      return {
        success: false,
        error: 'AI service is not available. Please check your API key configuration.'
      };
    }

    try {
      const prompt = `Please provide a simple, clear explanation of the following text or concept. Make it easy to understand:

${text}

Explanation:`;

      const result = await this.model!.generateContent(prompt);
      const response = await result.response;
      const explanation = response.text();

      return {
        success: true,
        content: explanation.trim()
      };
    } catch (error) {
      console.error('Error explaining text:', error);
      return {
        success: false,
        error: 'Failed to generate explanation. Please try again.'
      };
    }
  }

  /**
   * Brainstorm ideas based on text
   */
  static async brainstormIdeas(text: string): Promise<AIActionResult> {
    if (!this.isAvailable()) {
      return {
        success: false,
        error: 'AI service is not available. Please check your API key configuration.'
      };
    }

    try {
      const prompt = `Based on the following text, generate creative ideas, suggestions, or related concepts. Provide them as a list:

${text}

Ideas:`;

      const result = await this.model!.generateContent(prompt);
      const response = await result.response;
      const ideasText = response.text();

      // Parse the response into an array
      const ideas = ideasText
        .split('\n')
        .map(item => item.trim())
        .filter(item => item.length > 0)
        .map(item => item.replace(/^[-•*]\s*/, '')); // Remove bullet points

      return {
        success: true,
        ideas
      };
    } catch (error) {
      console.error('Error brainstorming ideas:', error);
      return {
        success: false,
        error: 'Failed to generate ideas. Please try again.'
      };
    }
  }

  /**
   * Smart search - find relevant content based on natural language query
   */
  static async smartSearch(query: string, notes: any[]): Promise<AIResponse> {
    if (!this.isAvailable()) {
      return {
        success: false,
        error: 'AI service is not available. Please check your API key configuration.'
      };
    }

    try {
      // Create a context of all notes for the AI to search through
      const notesContext = notes.map(note => 
        `Title: ${note.title}\nContent: ${note.content}\nTags: ${note.tags?.join(', ') || 'None'}\n---`
      ).join('\n');

      const prompt = `Given the following notes and a search query, identify which notes are most relevant to the query. Consider semantic meaning, not just keyword matching.

Notes:
${notesContext}

Search Query: "${query}"

Please list the most relevant note titles in order of relevance:`;

      const result = await this.model!.generateContent(prompt);
      const response = await result.response;
      const searchResults = response.text();

      return {
        success: true,
        content: searchResults.trim()
      };
    } catch (error) {
      console.error('Error performing smart search:', error);
      return {
        success: false,
        error: 'Failed to perform smart search. Please try again.'
      };
    }
  }
}
