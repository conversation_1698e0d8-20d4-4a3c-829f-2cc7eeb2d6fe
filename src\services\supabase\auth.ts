import * as LocalAuthentication from 'expo-local-authentication';
import { ApiResponse, LoginForm, SignupForm, User } from '../../types';
import { supabase, TABLES } from './config';

export class AuthService {
  // Sign up with email and password
  static async signUp(form: SignupForm): Promise<ApiResponse<User>> {
    try {
      const { data, error } = await supabase.auth.signUp({
        email: form.email,
        password: form.password,
        options: {
          data: {
            full_name: form.fullName,
          },
        },
      });

      if (error) {
        return { data: null, error: error.message, success: false };
      }

      if (data.user) {
        // Create user profile in our users table
        const userProfile = await this.createUserProfile(data.user.id, {
          email: form.email,
          full_name: form.fullName,
        });

        if (userProfile.error) {
          return { data: null, error: userProfile.error, success: false };
        }

        return { data: userProfile.data, error: null, success: true };
      }

      return { data: null, error: 'User creation failed', success: false };
    } catch (error) {
      return { 
        data: null, 
        error: error instanceof Error ? error.message : 'Unknown error', 
        success: false 
      };
    }
  }

  // Sign in with email and password
  static async signIn(form: LoginForm): Promise<ApiResponse<User>> {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: form.email,
        password: form.password,
      });

      if (error) {
        return { data: null, error: error.message, success: false };
      }

      if (data.user) {
        const userProfile = await this.getUserProfile(data.user.id);
        return userProfile;
      }

      return { data: null, error: 'Sign in failed', success: false };
    } catch (error) {
      return { 
        data: null, 
        error: error instanceof Error ? error.message : 'Unknown error', 
        success: false 
      };
    }
  }

  // Sign out
  static async signOut(): Promise<ApiResponse<null>> {
    try {
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        return { data: null, error: error.message, success: false };
      }

      return { data: null, error: null, success: true };
    } catch (error) {
      return { 
        data: null, 
        error: error instanceof Error ? error.message : 'Unknown error', 
        success: false 
      };
    }
  }

  // Get current session
  static async getCurrentSession() {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        return { data: null, error: error.message, success: false };
      }

      return { data: session, error: null, success: true };
    } catch (error) {
      return { 
        data: null, 
        error: error instanceof Error ? error.message : 'Unknown error', 
        success: false 
      };
    }
  }

  // Create user profile in database
  private static async createUserProfile(userId: string, userData: {
    email: string;
    full_name: string;
  }): Promise<ApiResponse<User>> {
    try {
      const userProfile: Partial<User> = {
        id: userId,
        email: userData.email,
        full_name: userData.full_name,
        subscription_tier: 'free',
        preferences: {
          theme: 'auto',
          biometric_enabled: false,
          auto_sync: true,
          ai_features_enabled: true,
        },
      };

      const { data, error } = await supabase
        .from(TABLES.USERS)
        .insert([userProfile])
        .select()
        .single();

      if (error) {
        return { data: null, error: error.message, success: false };
      }

      return { data: data as User, error: null, success: true };
    } catch (error) {
      return { 
        data: null, 
        error: error instanceof Error ? error.message : 'Unknown error', 
        success: false 
      };
    }
  }

  // Get user profile from database
  private static async getUserProfile(userId: string): Promise<ApiResponse<User>> {
    try {
      const { data, error } = await supabase
        .from(TABLES.USERS)
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        return { data: null, error: error.message, success: false };
      }

      return { data: data as User, error: null, success: true };
    } catch (error) {
      return { 
        data: null, 
        error: error instanceof Error ? error.message : 'Unknown error', 
        success: false 
      };
    }
  }

  // Update user profile
  static async updateUserProfile(userId: string, updates: Partial<User>): Promise<ApiResponse<User>> {
    try {
      const { data, error } = await supabase
        .from(TABLES.USERS)
        .update(updates)
        .eq('id', userId)
        .select()
        .single();

      if (error) {
        return { data: null, error: error.message, success: false };
      }

      return { data: data as User, error: null, success: true };
    } catch (error) {
      return { 
        data: null, 
        error: error instanceof Error ? error.message : 'Unknown error', 
        success: false 
      };
    }
  }

  // Check if biometric authentication is available
  static async isBiometricAvailable(): Promise<boolean> {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      return hasHardware && isEnrolled;
    } catch (error) {
      console.error('Error checking biometric availability:', error);
      return false;
    }
  }

  // Authenticate with biometrics
  static async authenticateWithBiometrics(): Promise<ApiResponse<boolean>> {
    try {
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Authenticate to access SuperNote',
        fallbackLabel: 'Use passcode',
      });

      if (result.success) {
        return { data: true, error: null, success: true };
      } else {
        return { data: false, error: 'Biometric authentication failed', success: false };
      }
    } catch (error) {
      return { 
        data: false, 
        error: error instanceof Error ? error.message : 'Unknown error', 
        success: false 
      };
    }
  }

  // Reset password
  static async resetPassword(email: string): Promise<ApiResponse<null>> {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email);
      
      if (error) {
        return { data: null, error: error.message, success: false };
      }

      return { data: null, error: null, success: true };
    } catch (error) {
      return { 
        data: null, 
        error: error instanceof Error ? error.message : 'Unknown error', 
        success: false 
      };
    }
  }
}
