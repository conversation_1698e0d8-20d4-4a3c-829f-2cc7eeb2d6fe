import { createClient } from '@supabase/supabase-js';
import { storage } from '../../utils/storage';


// Supabase configuration
const SUPABASE_URL = process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://wopwwhfilekqnvgeidwk.supabase.co';
const SUPABASE_ANON_KEY = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndvcHd3aGZpbGVrcW52Z2VpZHdrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzI0MzI4NzQsImV4cCI6MjA0ODAwODg3NH0.HcjchlMyaHDUq0Ey_c_SjQ_JJqJ006b';

console.log('Supabase URL:', SUPABASE_URL);
console.log('Supabase Key exists:', !!SUPABASE_ANON_KEY);

// Create Supabase client with cross-platform storage for session persistence
export const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
  auth: {
    storage: storage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
  realtime: {
    // Disable realtime for now to avoid compatibility issues
    params: {
      eventsPerSecond: 0,
    },
  },
});

// Database table names
export const TABLES = {
  USERS: 'users',
  NOTES: 'notes',
  FOLDERS: 'folders',
  TAGS: 'tags',
  NOTE_TAGS: 'note_tags',
  AUDIO_RECORDINGS: 'audio_recordings',
  AI_ACTIONS: 'ai_actions',
} as const;

// Storage bucket names
export const BUCKETS = {
  AUDIO: 'audio-recordings',
  AVATARS: 'avatars',
} as const;

// Database policies and RLS setup
export const DATABASE_POLICIES = {
  // Users can only access their own data
  USER_ISOLATION: 'auth.uid() = user_id',
  
  // Public read access for shared content (future feature)
  PUBLIC_READ: 'is_public = true',
} as const;
