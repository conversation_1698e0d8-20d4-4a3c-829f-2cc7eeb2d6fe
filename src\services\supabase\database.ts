import { User } from '@supabase/supabase-js';
import { supabase } from './config';

export class DatabaseService {
  /**
   * Initialize user profile in the database
   * This should be called after successful authentication
   */
  static async initializeUserProfile(user: User): Promise<boolean> {
    try {
      // Check if user profile already exists
      const { data: existingUser, error: checkError } = await supabase
        .from('users')
        .select('id')
        .eq('id', user.id)
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        // PGRST116 is "not found" error, which is expected for new users
        console.error('Error checking user profile:', checkError);
        return false;
      }

      // If user doesn't exist, create profile
      if (!existingUser) {
        const { error: insertError } = await supabase
          .from('users')
          .insert([
            {
              id: user.id,
              email: user.email || '',
              full_name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'User',
              preferences: {},
            },
          ]);

        if (insertError) {
          console.error('Error creating user profile:', insertError);
          return false;
        }

        console.log('User profile created successfully');
      }

      return true;
    } catch (error) {
      console.error('Error initializing user profile:', error);
      return false;
    }
  }

  /**
   * Test database connection and permissions
   */
  static async testConnection(): Promise<{
    success: boolean;
    message: string;
    details?: any;
  }> {
    try {
      // Test basic connection
      const { data: authUser, error: authError } = await supabase.auth.getUser();

      if (authError || !authUser.user) {
        return {
          success: false,
          message: 'User not authenticated',
          details: authError,
        };
      }

      // Test database access
      const { error: notesError } = await supabase
        .from('notes')
        .select('id')
        .limit(1);

      if (notesError) {
        return {
          success: false,
          message: 'Database access failed',
          details: notesError,
        };
      }

      // Test user profile access
      const { data: userProfile, error: profileError } = await supabase
        .from('users')
        .select('id, email')
        .eq('id', authUser.user.id)
        .single();

      if (profileError) {
        return {
          success: false,
          message: 'User profile access failed',
          details: profileError,
        };
      }

      return {
        success: true,
        message: 'Database connection successful',
        details: {
          userId: authUser.user.id,
          email: authUser.user.email,
          profileExists: !!userProfile,
        },
      };
    } catch (error) {
      return {
        success: false,
        message: 'Unexpected error during connection test',
        details: error,
      };
    }
  }

  /**
   * Create sample data for testing
   */
  static async createSampleData(userId: string): Promise<boolean> {
    try {
      // Create a sample folder
      const { data: folder, error: folderError } = await supabase
        .from('folders')
        .insert([
          {
            name: 'Getting Started',
            color: '#2196F3',
            user_id: userId,
          },
        ])
        .select()
        .single();

      if (folderError) {
        console.error('Error creating sample folder:', folderError);
        return false;
      }

      // Create sample notes
      const sampleNotes = [
        {
          title: 'Welcome to SuperNote!',
          content: `<h1>Welcome to SuperNote!</h1>
<p>This is your first note. SuperNote is an AI-powered note-taking app that helps you:</p>
<ul>
<li><strong>Organize</strong> your thoughts with folders and tags</li>
<li><strong>Search</strong> through your notes quickly</li>
<li><strong>Format</strong> your text with rich editing tools</li>
<li><strong>Sync</strong> across all your devices</li>
</ul>
<p>Try creating a new note by tapping the + button!</p>`,
          user_id: userId,
          folder_id: folder.id,
          tags: ['welcome', 'tutorial'],
          is_pinned: true,
        },
        {
          title: 'Quick Tips',
          content: `<h2>Quick Tips for SuperNote</h2>
<p><strong>Navigation:</strong></p>
<ul>
<li>Use the tabs at the bottom to switch between Notes, Search, and Folders</li>
<li>Tap the + button to create a new note</li>
<li>Swipe to refresh your notes list</li>
</ul>
<p><strong>Organization:</strong></p>
<ul>
<li>Create folders to organize your notes by topic</li>
<li>Add tags to notes for flexible categorization</li>
<li>Pin important notes to keep them at the top</li>
</ul>`,
          user_id: userId,
          folder_id: folder.id,
          tags: ['tips', 'tutorial'],
        },
      ];

      const { error: notesError } = await supabase
        .from('notes')
        .insert(sampleNotes);

      if (notesError) {
        console.error('Error creating sample notes:', notesError);
        return false;
      }

      console.log('Sample data created successfully');
      return true;
    } catch (error) {
      console.error('Error creating sample data:', error);
      return false;
    }
  }

  /**
   * Check if database tables exist and are accessible
   */
  static async checkDatabaseSetup(): Promise<{
    tablesExist: boolean;
    missingTables: string[];
    message: string;
  }> {
    const requiredTables = ['users', 'notes', 'folders', 'tags'];
    const missingTables: string[] = [];

    try {
      for (const table of requiredTables) {
        try {
          const { error } = await supabase
            .from(table)
            .select('id')
            .limit(1);

          if (error) {
            missingTables.push(table);
          }
        } catch (error) {
          console.error(`Error checking table ${table}:`, error);
          missingTables.push(table);
        }
      }

      const tablesExist = missingTables.length === 0;

      return {
        tablesExist,
        missingTables,
        message: tablesExist
          ? 'All required tables exist'
          : `Missing tables: ${missingTables.join(', ')}. Please run the database setup script.`,
      };
    } catch (error) {
      console.error('Error checking database setup:', error);
      return {
        tablesExist: false,
        missingTables: requiredTables,
        message: 'Unable to check database setup. Please ensure you have proper access.',
      };
    }
  }

  /**
   * Get database statistics for debugging
   */
  static async getDatabaseStats(userId: string): Promise<{
    notesCount: number;
    foldersCount: number;
    tagsCount: number;
  }> {
    try {
      const [notesResult, foldersResult, tagsResult] = await Promise.all([
        supabase
          .from('notes')
          .select('id', { count: 'exact' })
          .eq('user_id', userId),
        supabase
          .from('folders')
          .select('id', { count: 'exact' })
          .eq('user_id', userId),
        supabase
          .from('tags')
          .select('id', { count: 'exact' })
          .eq('user_id', userId),
      ]);

      return {
        notesCount: notesResult.count || 0,
        foldersCount: foldersResult.count || 0,
        tagsCount: tagsResult.count || 0,
      };
    } catch (error) {
      console.error('Error getting database stats:', error);
      return {
        notesCount: 0,
        foldersCount: 0,
        tagsCount: 0,
      };
    }
  }
}
