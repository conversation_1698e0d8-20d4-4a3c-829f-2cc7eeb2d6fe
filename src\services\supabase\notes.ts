import { ApiR<PERSON>ponse, Folder, Note, NoteForm, PaginatedResponse, SearchFilters, Tag } from '../../types';
import { supabase, TABLES } from './config';

export class NotesService {
  // Notes CRUD operations
  static async createNote(noteData: NoteForm, userId: string): Promise<ApiResponse<Note>> {
    try {
      const note: Partial<Note> = {
        title: noteData.title || 'Untitled',
        content: noteData.content,
        content_type: 'rich_text',
        user_id: userId,
        folder_id: noteData.folderId,
        tags: noteData.tags,
        is_pinned: noteData.isPinned,
        is_archived: false,
        word_count: this.calculateWordCount(noteData.content),
        reading_time_minutes: this.calculateReadingTime(noteData.content),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        last_synced_at: new Date().toISOString(),
        audio_url: undefined,
      };

      const { data, error } = await supabase
        .from(TABLES.NOTES)
        .insert([note])
        .select()
        .single();

      if (error) {
        console.error('Error creating note:', error);
        return { data: null, error: error.message, success: false };
      }

      return { data: data as Note, error: null, success: true };
    } catch (error) {
      console.error('Exception in createNote:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
        success: false
      };
    }
  }

  static async updateNote(noteId: string, updates: Partial<Note>): Promise<ApiResponse<Note>> {
    try {
      // Update word count and reading time if content changed
      if (updates.content) {
        updates.word_count = this.calculateWordCount(updates.content);
        updates.reading_time_minutes = this.calculateReadingTime(updates.content);
      }

      const { data, error } = await supabase
        .from(TABLES.NOTES)
        .update(updates)
        .eq('id', noteId)
        .select()
        .single();

      if (error) {
        console.error('Error updating note:', error);
        return { data: null, error: error.message, success: false };
      }

      return { data: data as Note, error: null, success: true };
    } catch (error) {
      console.error('Exception in updateNote:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
        success: false
      };
    }
  }

  static async deleteNote(noteId: string): Promise<ApiResponse<null>> {
    try {
      const { error } = await supabase
        .from(TABLES.NOTES)
        .delete()
        .eq('id', noteId);

      if (error) {
        console.error('Error deleting note:', error);
        return { data: null, error: error.message, success: false };
      }

      return { data: null, error: null, success: true };
    } catch (error) {
      console.error('Exception in deleteNote:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
        success: false
      };
    }
  }

  static async getNote(noteId: string): Promise<ApiResponse<Note>> {
    try {
      const { data, error } = await supabase
        .from(TABLES.NOTES)
        .select('*')
        .eq('id', noteId)
        .single();

      if (error) {
        console.error('Error fetching note:', error);
        return { data: null, error: error.message, success: false };
      }

      return { data: data as Note, error: null, success: true };
    } catch (error) {
      console.error('Exception in getNote:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
        success: false
      };
    }
  }

  static async getNotes(
    userId: string,
    page: number = 1,
    limit: number = 20,
    filters?: SearchFilters
  ): Promise<PaginatedResponse<Note>> {
    try {
      let query = supabase
        .from(TABLES.NOTES)
        .select('*', { count: 'exact' })
        .eq('user_id', userId)
        .eq('is_archived', false);

      // Apply filters
      if (filters) {
        if (filters.folders && filters.folders.length > 0) {
          query = query.in('folder_id', filters.folders);
        }

        if (filters.tags && filters.tags.length > 0) {
          query = query.overlaps('tags', filters.tags);
        }

        if (filters.dateRange && filters.dateRange.start) {
          query = query.gte('created_at', filters.dateRange.start);
        }

        if (filters.dateRange && filters.dateRange.end) {
          query = query.lte('created_at', filters.dateRange.end);
        }

        // Apply sorting
        const isAsc = filters.sortOrder === 'asc';
        query = query.order(filters.sortBy || 'updated_at', { ascending: isAsc });
      } else {
        // Default sorting by updated_at desc
        query = query.order('updated_at', { ascending: false });
      }

      // Apply pagination
      const from = (page - 1) * limit;
      const to = from + limit - 1;
      query = query.range(from, to);

      const { data, error, count } = await query;

      if (error) {
        return {
          data: [],
          count: 0,
          page,
          limit,
          hasMore: false,
          total: 0,
          totalPages: 0
        };
      }

      const totalCount = count || 0;
      const hasMore = (page * limit) < totalCount;

      return {
        data: data as Note[],
        count: totalCount,
        page,
        limit,
        hasMore,
        total: totalCount,
        totalPages: Math.ceil(totalCount / limit)
      };
    } catch (error) {
      console.error('Error in getNotes:', error);
      return {
        data: [],
        count: 0,
        page,
        limit,
        hasMore: false,
        total: 0,
        totalPages: 0
      };
    }
  }

  static async searchNotes(userId: string, query: string): Promise<ApiResponse<Note[]>> {
    try {
      // Use parameterized queries to prevent SQL injection
      const searchTerm = `%${query}%`;
      const { data, error } = await supabase
        .from(TABLES.NOTES)
        .select('*')
        .eq('user_id', userId)
        .eq('is_archived', false)
        .or(`title.ilike.${searchTerm},content.ilike.${searchTerm}`)
        .order('updated_at', { ascending: false });

      if (error) {
        return { data: [], error: error.message, success: false };
      }

      return { data: data as Note[], error: null, success: true };
    } catch (error) {
      return {
        data: [],
        error: error instanceof Error ? error.message : 'Unknown error',
        success: false
      };
    }
  }

  // Folder operations
  static async createFolder(name: string, color: string, userId: string, parentId?: string): Promise<ApiResponse<Folder>> {
    try {
      const folder: Partial<Folder> = {
        name,
        color,
        user_id: userId,
        parent_folder_id: parentId,
        notes_count: 0,
      };

      const { data, error } = await supabase
        .from(TABLES.FOLDERS)
        .insert([folder])
        .select()
        .single();

      if (error) {
        return { data: null, error: error.message, success: false };
      }

      return { data: data as Folder, error: null, success: true };
    } catch (error) {
      return { 
        data: null, 
        error: error instanceof Error ? error.message : 'Unknown error', 
        success: false 
      };
    }
  }

  static async getFolders(userId: string): Promise<ApiResponse<Folder[]>> {
    try {
      const { data, error } = await supabase
        .from(TABLES.FOLDERS)
        .select('*')
        .eq('user_id', userId)
        .order('name');

      if (error) {
        return { data: [], error: error.message, success: false };
      }

      return { data: data as Folder[], error: null, success: true };
    } catch (error) {
      return {
        data: [],
        error: error instanceof Error ? error.message : 'Unknown error',
        success: false
      };
    }
  }

  // Tag operations
  static async createTag(name: string, color: string, userId: string): Promise<ApiResponse<Tag>> {
    try {
      const tag: Partial<Tag> = {
        name,
        color,
        user_id: userId,
        usage_count: 0,
      };

      const { data, error } = await supabase
        .from(TABLES.TAGS)
        .insert([tag])
        .select()
        .single();

      if (error) {
        return { data: null, error: error.message, success: false };
      }

      return { data: data as Tag, error: null, success: true };
    } catch (error) {
      return { 
        data: null, 
        error: error instanceof Error ? error.message : 'Unknown error', 
        success: false 
      };
    }
  }

  static async getTags(userId: string): Promise<ApiResponse<Tag[]>> {
    try {
      const { data, error } = await supabase
        .from(TABLES.TAGS)
        .select('*')
        .eq('user_id', userId)
        .order('usage_count', { ascending: false });

      if (error) {
        return { data: [], error: error.message, success: false };
      }

      return { data: data as Tag[], error: null, success: true };
    } catch (error) {
      return { 
        data: [], 
        error: error instanceof Error ? error.message : 'Unknown error', 
        success: false 
      };
    }
  }

  // Utility functions
  private static calculateWordCount(content: string): number {
    return content.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  private static calculateReadingTime(content: string): number {
    const wordsPerMinute = 200; // Average reading speed
    const wordCount = this.calculateWordCount(content);
    return Math.ceil(wordCount / wordsPerMinute);
  }
}
