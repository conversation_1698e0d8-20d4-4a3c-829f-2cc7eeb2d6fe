import { create } from 'zustand';
import { OnboardingService } from '../services/storage/onboardingService';
import { AuthService } from '../services/supabase/auth';
import { supabase } from '../services/supabase/config';
import { DatabaseService } from '../services/supabase/database';
import { AuthState, LoginForm, SignupForm, User } from '../types';

interface AuthStore extends AuthState {
  // Database state
  databaseReady: boolean;
  databaseError: string | null;

  // Onboarding state
  isFirstTimeUser: boolean;
  hasCompletedOnboarding: boolean;
  shouldShowOnboarding: boolean;
  isInitializing: boolean;

  // Actions
  signIn: (form: LoginForm) => Promise<boolean>;
  signUp: (form: SignupForm) => Promise<boolean>;
  signOut: () => Promise<void>;
  checkSession: () => Promise<void>;
  updateUser: (updates: Partial<User>) => Promise<boolean>;
  enableBiometric: () => Promise<boolean>;
  authenticateWithBiometric: () => Promise<boolean>;
  resetPassword: (email: string) => Promise<boolean>;
  setLoading: (loading: boolean) => void;

  // Onboarding actions
  initializeApp: () => Promise<void>;
  completeOnboarding: () => Promise<void>;
  checkOnboardingStatus: () => Promise<void>;

  // Database actions
  initializeDatabase: () => Promise<boolean>;
  testDatabaseConnection: () => Promise<void>;
  createSampleData: () => Promise<boolean>;
}

export const useAuthStore = create<AuthStore>((set, get) => ({
  // Initial state
  user: null,
  session: null,
  isLoading: false,
  isAuthenticated: false,
  databaseReady: false,
  databaseError: null,

  // Onboarding state
  isFirstTimeUser: true,
  hasCompletedOnboarding: false,
  shouldShowOnboarding: false,
  isInitializing: true,

  // Actions
  signIn: async (form: LoginForm) => {
    set({ isLoading: true });
    
    try {
      const result = await AuthService.signIn(form);
      
      if (result.success && result.data) {
        set({ 
          user: result.data, 
          isAuthenticated: true, 
          isLoading: false 
        });
        return true;
      } else {
        set({ isLoading: false });
        return false;
      }
    } catch (error) {
      console.error('Error in signIn:', error);
      set({ isLoading: false });
      return false;
    }
  },

  signUp: async (form: SignupForm) => {
    set({ isLoading: true });
    
    try {
      const result = await AuthService.signUp(form);
      
      if (result.success && result.data) {
        set({ 
          user: result.data, 
          isAuthenticated: true, 
          isLoading: false 
        });
        return true;
      } else {
        set({ isLoading: false });
        return false;
      }
    } catch (error) {
      console.error('Error in signUp:', error);
      set({ isLoading: false });
      return false;
    }
  },

  signOut: async () => {
    set({ isLoading: true });
    
    try {
      await AuthService.signOut();
      set({ 
        user: null, 
        session: null, 
        isAuthenticated: false, 
        isLoading: false 
      });
    } catch (error) {
      console.error('Error in signOut:', error);
      set({ isLoading: false });
    }
  },

  checkSession: async () => {
    set({ isLoading: true });

    try {
      const sessionResult = await AuthService.getCurrentSession();

      if (sessionResult.success && sessionResult.data) {
        const { user } = sessionResult.data;

        if (user) {
          // For now, create a basic user object from auth user
          // In a real app, you'd fetch the full profile from your users table
          const userProfile = {
            id: user.id,
            email: user.email || '',
            full_name: user.user_metadata?.full_name || '',
            avatar_url: user.user_metadata?.avatar_url,
            created_at: user.created_at,
            updated_at: user.updated_at || user.created_at,
            subscription_tier: 'free' as const,
            preferences: {
              theme: 'auto' as const,
              biometric_enabled: false,
              auto_sync: true,
              ai_features_enabled: true,
            },
          };

          set({
            user: userProfile,
            session: sessionResult.data,
            isAuthenticated: true,
            isLoading: false
          });

          // Initialize database after successful authentication
          get().initializeDatabase();
        } else {
          set({ isLoading: false });
        }
      } else {
        // No session found - user is not authenticated
        set({
          user: null,
          session: null,
          isAuthenticated: false,
          isLoading: false
        });
      }
    } catch (error) {
      console.error('Error in checkSession:', error);

      // Set loading to false and keep user as null (not authenticated)
      set({
        user: null,
        session: null,
        isAuthenticated: false,
        isLoading: false
      });
    }
  },

  updateUser: async (updates: Partial<User>) => {
    const { user } = get();
    if (!user) return false;

    set({ isLoading: true });
    
    try {
      const result = await AuthService.updateUserProfile(user.id, updates);
      
      if (result.success && result.data) {
        set({ 
          user: result.data, 
          isLoading: false 
        });
        return true;
      } else {
        set({ isLoading: false });
        return false;
      }
    } catch (error) {
      console.error('Error in updateUser:', error);
      set({ isLoading: false });
      return false;
    }
  },

  enableBiometric: async () => {
    const { user } = get();
    if (!user) return false;

    const isAvailable = await AuthService.isBiometricAvailable();
    if (!isAvailable) return false;

    return await get().updateUser({
      preferences: {
        ...user.preferences,
        biometric_enabled: true,
      },
    });
  },

  authenticateWithBiometric: async () => {
    const result = await AuthService.authenticateWithBiometrics();
    return result.success && result.data === true;
  },

  resetPassword: async (email: string) => {
    set({ isLoading: true });
    
    try {
      const result = await AuthService.resetPassword(email);
      set({ isLoading: false });
      return result.success;
    } catch (error) {
      console.error('Error in resetPassword:', error);
      set({ isLoading: false });
      return false;
    }
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  // Onboarding actions
  initializeApp: async () => {
    set({ isInitializing: true });

    try {
      // Increment launch count
      await OnboardingService.incrementLaunchCount();

      // Check onboarding status
      const [isFirstTime, hasCompleted, shouldShow] = await Promise.all([
        OnboardingService.isFirstTimeUser(),
        OnboardingService.hasCompletedOnboarding(),
        OnboardingService.shouldShowOnboarding(),
      ]);

      set({
        isFirstTimeUser: isFirstTime,
        hasCompletedOnboarding: hasCompleted,
        shouldShowOnboarding: shouldShow,
        isInitializing: false,
      });

      // Check session after onboarding state is set
      await get().checkSession();
    } catch (error) {
      console.error('Error initializing app:', error);
      set({
        isFirstTimeUser: true,
        hasCompletedOnboarding: false,
        shouldShowOnboarding: true,
        isInitializing: false,
      });
    }
  },

  completeOnboarding: async () => {
    try {
      await OnboardingService.markOnboardingCompleted();
      set({
        hasCompletedOnboarding: true,
        shouldShowOnboarding: false,
        isFirstTimeUser: false,
      });
    } catch (error) {
      console.error('Error completing onboarding:', error);
    }
  },

  checkOnboardingStatus: async () => {
    try {
      const [isFirstTime, hasCompleted, shouldShow] = await Promise.all([
        OnboardingService.isFirstTimeUser(),
        OnboardingService.hasCompletedOnboarding(),
        OnboardingService.shouldShowOnboarding(),
      ]);

      set({
        isFirstTimeUser: isFirstTime,
        hasCompletedOnboarding: hasCompleted,
        shouldShowOnboarding: shouldShow,
      });
    } catch (error) {
      console.error('Error checking onboarding status:', error);
    }
  },

  // Database actions
  initializeDatabase: async () => {
    const { user } = get();
    if (!user) return false;

    set({ isLoading: true, databaseError: null });

    try {

      // Check database setup
      const setupCheck = await DatabaseService.checkDatabaseSetup();
      if (!setupCheck.tablesExist) {
        set({
          databaseError: setupCheck.message,
          isLoading: false
        });
        return false;
      }

      // Initialize user profile
      const profileInitialized = await DatabaseService.initializeUserProfile({
        id: user.id,
        email: user.email,
        user_metadata: { full_name: user.full_name },
      } as any);

      if (!profileInitialized) {
        set({
          databaseError: 'Failed to initialize user profile',
          isLoading: false
        });
        return false;
      }

      // Test connection
      const connectionTest = await DatabaseService.testConnection();
      if (!connectionTest.success) {
        set({
          databaseError: connectionTest.message,
          isLoading: false
        });
        return false;
      }

      set({
        databaseReady: true,
        databaseError: null,
        isLoading: false
      });
      return true;
    } catch (error) {
      set({
        databaseError: error instanceof Error ? error.message : 'Unknown database error',
        isLoading: false
      });
      return false;
    }
  },

  testDatabaseConnection: async () => {
    set({ isLoading: true, databaseError: null });

    try {
      const result = await DatabaseService.testConnection();
      set({
        databaseReady: result.success,
        databaseError: result.success ? null : result.message,
        isLoading: false
      });
    } catch (error) {
      set({
        databaseReady: false,
        databaseError: error instanceof Error ? error.message : 'Connection test failed',
        isLoading: false
      });
    }
  },

  createSampleData: async () => {
    const { user } = get();
    if (!user) return false;

    set({ isLoading: true });

    try {
      const success = await DatabaseService.createSampleData(user.id);
      set({ isLoading: false });
      return success;
    } catch (error) {
      console.error('Error in createSampleData:', error);
      set({ isLoading: false });
      return false;
    }
  },
}));

// Set up auth state listener
supabase.auth.onAuthStateChange((event, _session) => {
  const { checkSession } = useAuthStore.getState();

  if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
    checkSession();
  } else if (event === 'SIGNED_OUT') {
    useAuthStore.setState({
      user: null,
      session: null,
      isAuthenticated: false
    });
  }
});
