import { create } from 'zustand';
import { supabase } from '../services/supabase/config';
import { NotesService } from '../services/supabase/notes';
import { Folder } from '../types/folder';

interface FoldersState {
  folders: Folder[];
  isLoading: boolean;
  error: string | null;
  
  // Actions
  loadFolders: (userId: string) => Promise<void>;
  createFolder: (name: string, userId: string) => Promise<boolean>;
  updateFolder: (id: string, updates: Partial<Folder>) => Promise<boolean>;
  deleteFolder: (id: string) => Promise<boolean>;
  clearFolders: () => void;
}

export const useFoldersStore = create<FoldersState>((set, get) => ({
  folders: [],
  isLoading: false,
  error: null,

  loadFolders: async (userId: string) => {
    set({ isLoading: true, error: null });

    try {
      const result = await NotesService.getFolders(userId);

      if (result.success) {
        set({ folders: result.data || [], isLoading: false });
      } else {
        throw new Error(result.error || 'Failed to load folders');
      }
    } catch (error) {
      console.error('Error loading folders:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to load folders',
        isLoading: false
      });
    }
  },

  createFolder: async (name: string, userId: string) => {
    set({ isLoading: true, error: null });
    
    try {
      const newFolder: Omit<Folder, 'id'> = {
        name: name.trim(),
        user_id: userId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const { data, error } = await supabase
        .from('folders')
        .insert([newFolder])
        .select()
        .single();

      if (error) throw error;

      const { folders } = get();
      set({ 
        folders: [...folders, data],
        isLoading: false 
      });

      return true;
    } catch (error) {
      console.error('Error creating folder:', error);
      set({ 
        error: error instanceof Error ? error.message : 'Failed to create folder',
        isLoading: false 
      });
      return false;
    }
  },

  updateFolder: async (id: string, updates: Partial<Folder>) => {
    set({ isLoading: true, error: null });
    
    try {
      const { data, error } = await supabase
        .from('folders')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      const { folders } = get();
      set({ 
        folders: folders.map(folder => 
          folder.id === id ? data : folder
        ),
        isLoading: false 
      });

      return true;
    } catch (error) {
      console.error('Error updating folder:', error);
      set({ 
        error: error instanceof Error ? error.message : 'Failed to update folder',
        isLoading: false 
      });
      return false;
    }
  },

  deleteFolder: async (id: string) => {
    set({ isLoading: true, error: null });
    
    try {
      const { error } = await supabase
        .from('folders')
        .delete()
        .eq('id', id);

      if (error) throw error;

      const { folders } = get();
      set({ 
        folders: folders.filter(folder => folder.id !== id),
        isLoading: false 
      });

      return true;
    } catch (error) {
      console.error('Error deleting folder:', error);
      set({ 
        error: error instanceof Error ? error.message : 'Failed to delete folder',
        isLoading: false 
      });
      return false;
    }
  },

  clearFolders: () => {
    set({ folders: [], isLoading: false, error: null });
  },
}));
