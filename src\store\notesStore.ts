import { create } from 'zustand';
import { NotesService } from '../services/supabase/notes';
import { Note, NoteForm, NotesState, SearchFilters } from '../types';

interface NotesStore extends NotesState {
  // Actions
  createNote: (noteData: NoteForm, userId: string) => Promise<boolean>;
  updateNote: (noteId: string, updates: Partial<Note>) => Promise<boolean>;
  deleteNote: (noteId: string) => Promise<boolean>;
  loadNotes: (userId: string, page?: number, filters?: SearchFilters) => Promise<void>;
  loadNote: (noteId: string) => Promise<boolean>;
  searchNotes: (userId: string, query: string) => Promise<void>;
  setCurrentNote: (note: Note | null) => void;
  
  // Folder actions
  createFolder: (name: string, color: string, userId: string, parentId?: string) => Promise<boolean>;
  loadFolders: (userId: string) => Promise<void>;
  setSelectedFolder: (folderId: string | null) => void;
  
  // Tag actions
  createTag: (name: string, color: string, userId: string) => Promise<boolean>;
  loadTags: (userId: string) => Promise<void>;
  setSelectedTags: (tags: string[]) => void;
  
  // UI actions
  setLoading: (loading: boolean) => void;
  setSearchQuery: (query: string) => void;
  clearSearch: () => void;
}

export const useNotesStore = create<NotesStore>((set, get) => ({
  // Initial state
  notes: [],
  folders: [],
  tags: [],
  currentNote: null,
  searchQuery: '',
  searchResults: [],
  isLoading: false,
  lastSyncTime: null,
  selectedFolder: null,
  selectedTags: [],

  // Note actions
  createNote: async (noteData: NoteForm, userId: string) => {
    console.log('NotesStore.createNote called with:', { noteData, userId });
    set({ isLoading: true });

    try {
      const result = await NotesService.createNote(noteData, userId);
      console.log('NotesService.createNote result:', result);

      if (result.success && result.data) {
        const { notes } = get();
        set({
          notes: [result.data, ...notes],
          currentNote: result.data,
          isLoading: false
        });
        console.log('Note created successfully, updated store');
        return true;
      } else {
        console.log('Note creation failed:', result.error);
        set({ isLoading: false });
        return false;
      }
    } catch (error) {
      console.error('Error in createNote:', error);
      set({ isLoading: false });
      return false;
    }
  },

  updateNote: async (noteId: string, updates: Partial<Note>) => {
    set({ isLoading: true });
    
    try {
      const result = await NotesService.updateNote(noteId, updates);
      
      if (result.success && result.data) {
        const { notes, currentNote } = get();
        const updatedNotes = notes.map(note => 
          note.id === noteId ? result.data! : note
        );
        
        set({ 
          notes: updatedNotes,
          currentNote: currentNote?.id === noteId ? result.data : currentNote,
          isLoading: false 
        });
        return true;
      } else {
        set({ isLoading: false });
        return false;
      }
    } catch (error) {
      console.error('Error in updateNote:', error);
      set({ isLoading: false });
      return false;
    }
  },

  deleteNote: async (noteId: string) => {
    set({ isLoading: true });
    
    try {
      const result = await NotesService.deleteNote(noteId);
      
      if (result.success) {
        const { notes, currentNote } = get();
        const filteredNotes = notes.filter(note => note.id !== noteId);
        
        set({ 
          notes: filteredNotes,
          currentNote: currentNote?.id === noteId ? null : currentNote,
          isLoading: false 
        });
        return true;
      } else {
        set({ isLoading: false });
        return false;
      }
    } catch (error) {
      console.error('Error in deleteNote:', error);
      set({ isLoading: false });
      return false;
    }
  },

  loadNotes: async (userId: string, page = 1, filters?: SearchFilters) => {
    set({ isLoading: true });
    
    try {
      const result = await NotesService.getNotes(userId, page, 20, filters);
      
      if (page === 1) {
        // First page - replace notes
        set({ 
          notes: result.data,
          isLoading: false,
          lastSyncTime: new Date().toISOString()
        });
      } else {
        // Additional pages - append notes
        const { notes } = get();
        set({ 
          notes: [...notes, ...result.data],
          isLoading: false 
        });
      }
    } catch (error) {
      console.error('Error in loadNotes:', error);
      set({ isLoading: false });
    }
  },

  loadNote: async (noteId: string) => {
    set({ isLoading: true });
    
    try {
      const result = await NotesService.getNote(noteId);
      
      if (result.success && result.data) {
        set({ 
          currentNote: result.data,
          isLoading: false 
        });
        return true;
      } else {
        set({ isLoading: false });
        return false;
      }
    } catch (error) {
      console.error('Error in loadNote:', error);
      set({ isLoading: false });
      return false;
    }
  },

  searchNotes: async (userId: string, query: string) => {
    set({ isLoading: true, searchQuery: query });
    
    try {
      const result = await NotesService.searchNotes(userId, query);
      
      if (result.success) {
        set({ 
          searchResults: result.data || [],
          isLoading: false 
        });
      } else {
        set({ 
          searchResults: [],
          isLoading: false 
        });
      }
    } catch (error) {
      console.error('Error in searchNotes:', error);
      set({
        searchResults: [],
        isLoading: false
      });
    }
  },

  setCurrentNote: (note: Note | null) => {
    set({ currentNote: note });
  },

  // Folder actions
  createFolder: async (name: string, color: string, userId: string, parentId?: string) => {
    set({ isLoading: true });
    
    try {
      const result = await NotesService.createFolder(name, color, userId, parentId);
      
      if (result.success && result.data) {
        const { folders } = get();
        set({ 
          folders: [...folders, result.data],
          isLoading: false 
        });
        return true;
      } else {
        set({ isLoading: false });
        return false;
      }
    } catch (error) {
      console.error('Error in createFolder:', error);
      set({ isLoading: false });
      return false;
    }
  },

  loadFolders: async (userId: string) => {
    try {
      const result = await NotesService.getFolders(userId);
      
      if (result.success) {
        set({ folders: result.data || [] });
      }
    } catch (error) {
      console.error('Error in loadFolders:', error);
      set({ folders: [] });
    }
  },

  setSelectedFolder: (folderId: string | null) => {
    set({ selectedFolder: folderId });
  },

  // Tag actions
  createTag: async (name: string, color: string, userId: string) => {
    set({ isLoading: true });
    
    try {
      const result = await NotesService.createTag(name, color, userId);
      
      if (result.success && result.data) {
        const { tags } = get();
        set({ 
          tags: [...tags, result.data],
          isLoading: false 
        });
        return true;
      } else {
        set({ isLoading: false });
        return false;
      }
    } catch (error) {
      console.error('Error in createTag:', error);
      set({ isLoading: false });
      return false;
    }
  },

  loadTags: async (userId: string) => {
    try {
      const result = await NotesService.getTags(userId);
      
      if (result.success) {
        set({ tags: result.data || [] });
      }
    } catch (error) {
      console.error('Error in loadTags:', error);
      set({ tags: [] });
    }
  },

  setSelectedTags: (tags: string[]) => {
    set({ selectedTags: tags });
  },

  // UI actions
  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  setSearchQuery: (query: string) => {
    set({ searchQuery: query });
  },

  clearSearch: () => {
    set({ searchQuery: '', searchResults: [] });
  },
}));
