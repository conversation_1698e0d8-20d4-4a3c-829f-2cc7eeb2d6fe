// Core data types for SuperNote app

export interface User {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
  subscription_tier: 'free' | 'premium';
  preferences: UserPreferences;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  biometric_enabled: boolean;
  auto_sync: boolean;
  ai_features_enabled: boolean;
  default_folder_id?: string;
}

export interface Note {
  id: string;
  title: string;
  content: string;
  content_type: 'rich_text' | 'plain_text';
  user_id: string;
  folder_id?: string;
  tags: string[];
  is_pinned: boolean;
  is_archived: boolean;
  is_public: boolean;
  created_at: string;
  updated_at: string;
  last_synced_at?: string;
  audio_url?: string;
  ai_summary?: string;
  word_count: number;
  reading_time_minutes: number;
}

export interface Folder {
  id: string;
  name: string;
  color: string;
  user_id: string;
  parent_folder_id?: string;
  created_at: string;
  updated_at: string;
  notes_count: number;
}

export interface Tag {
  id: string;
  name: string;
  color: string;
  user_id: string;
  created_at: string;
  usage_count: number;
}

export interface AudioRecording {
  id: string;
  note_id: string;
  file_url: string;
  duration_seconds: number;
  transcription?: string;
  created_at: string;
}

// AI-related types
export interface AIAction {
  type: 'summarize' | 'improve_writing' | 'find_action_items' | 'explain' | 'brainstorm';
  input_text: string;
  result: string;
  created_at: string;
}

export interface AISearchQuery {
  query: string;
  results: Note[];
  created_at: string;
}

// App state types
export interface AuthState {
  user: User | null;
  session: any | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

export interface NotesState {
  notes: Note[];
  folders: Folder[];
  tags: Tag[];
  currentNote: Note | null;
  searchQuery: string;
  searchResults: Note[];
  isLoading: boolean;
  lastSyncTime: string | null;
  selectedFolder: string | null;
  selectedTags: string[];
}

export interface AppState {
  isOnboardingComplete: boolean;
  isOffline: boolean;
  theme: 'light' | 'dark' | 'auto';
  biometricEnabled: boolean;
  aiFeatures: {
    summarizationEnabled: boolean;
    transcriptionEnabled: boolean;
    smartSearchEnabled: boolean;
    contentGenerationEnabled: boolean;
  };
}

// Navigation types
export type RootStackParamList = {
  Onboarding: undefined;
  Auth: undefined;
  Main: undefined;
  NoteEditor: { noteId?: string; folderId?: string };
  Settings: undefined;
  FolderManager: undefined;
  TagManager: undefined;
};

export type TabParamList = {
  Home: undefined;
  Search: undefined;
  Folders: undefined;
  Settings: undefined;
};

// API response types
export interface ApiResponse<T> {
  data: T | null;
  error: string | null;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  count: number;
  page: number;
  limit: number;
  hasMore: boolean;
  total: number;
  totalPages: number;
}

// Form types
export interface LoginForm {
  email: string;
  password: string;
}

export interface SignupForm {
  email: string;
  password: string;
  confirmPassword: string;
  fullName: string;
}

export interface NoteForm {
  title: string;
  content: string;
  folderId?: string;
  tags: string[];
  isPinned: boolean;
}

// Search and filter types
export interface SearchFilters {
  search?: string;
  folders: string[];
  tags: string[];
  folderId?: string;
  isPinned?: boolean;
  isArchived?: boolean;
  dateRange: {
    start?: string;
    end?: string;
  };
  contentType: 'all' | 'text' | 'audio';
  sortBy: 'updated_at' | 'created_at' | 'title' | 'relevance';
  sortOrder: 'asc' | 'desc';
}

// Error types
export interface AppError {
  code: string;
  message: string;
  details?: any;
}

// Sync types
export interface SyncStatus {
  isOnline: boolean;
  lastSyncTime: string | null;
  pendingChanges: number;
  isSyncing: boolean;
}

export interface PendingChange {
  id: string;
  type: 'create' | 'update' | 'delete';
  entity: 'note' | 'folder' | 'tag';
  data: any;
  timestamp: string;
}
