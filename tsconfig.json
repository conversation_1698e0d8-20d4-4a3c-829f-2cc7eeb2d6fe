{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*", "./components/*"], "@/screens/*": ["./src/screens/*"], "@/services/*": ["./src/services/*"], "@/store/*": ["./src/store/*"], "@/types/*": ["./src/types/*"], "@/utils/*": ["./src/utils/*"], "@/constants/*": ["./constants/*"], "@/hooks/*": ["./hooks/*"], "@/src/*": ["./src/*"]}}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts"]}